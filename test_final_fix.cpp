#include "cpp_implementation/include/ObstacleTypeManager.h"
#include <iostream>
#include <memory>

int main() {
    std::cout << "测试最终编译修复..." << std::endl;
    
    // 创建障碍物管理器
    ObstacleTypeManager manager(10, 10, 5);
    
    // 测试添加普通障碍物类型
    try {
        manager.add_obstacle_type("test_obstacle", "Test obstacle", "2024-01-01");
        std::cout << "✓ 添加普通障碍物类型成功" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "✗ 添加普通障碍物类型失败: " << e.what() << std::endl;
        return 1;
    }
    
    // 测试添加圆柱形禁飞区
    try {
        auto geometry = std::make_unique<CylindricalNFZGeometry>(5.0f, 5.0f, 2.0f);
        manager.add_nfz_type("test_cylinder", "Test cylindrical NFZ", "2024-01-01", std::move(geometry));
        std::cout << "✓ 添加圆柱形禁飞区成功" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "✗ 添加圆柱形禁飞区失败: " << e.what() << std::endl;
        return 1;
    }
    
    // 测试添加多边形禁飞区
    try {
        std::vector<std::pair<float, float>> vertices = {{1.0f, 1.0f}, {3.0f, 1.0f}, {3.0f, 3.0f}, {1.0f, 3.0f}};
        auto geometry = std::make_unique<PolygonalNFZGeometry>(vertices);
        manager.add_nfz_type("test_polygon", "Test polygonal NFZ", "2024-01-01", std::move(geometry));
        std::cout << "✓ 添加多边形禁飞区成功" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "✗ 添加多边形禁飞区失败: " << e.what() << std::endl;
        return 1;
    }
    
    // 测试拷贝构造函数（这是关键测试）
    try {
        ObstacleTypeManager manager2 = manager; // 这会触发拷贝构造函数
        std::cout << "✓ 拷贝构造函数测试成功" << std::endl;
        
        // 测试拷贝后的功能
        std::string result = manager2.is_point_inside_any_nfz_2d(5.0f, 5.0f);
        if (result == "test_cylinder") {
            std::cout << "✓ 拷贝后的圆柱形禁飞区点检查成功" << std::endl;
        } else {
            std::cout << "✗ 拷贝后的圆柱形禁飞区点检查失败，期望 'test_cylinder'，得到 '" << result << "'" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ 拷贝构造函数测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    // 测试点检查功能
    try {
        // 测试圆柱形禁飞区内的点
        std::string result = manager.is_point_inside_any_nfz_2d(5.0f, 5.0f);
        if (result == "test_cylinder") {
            std::cout << "✓ 圆柱形禁飞区点检查成功" << std::endl;
        } else {
            std::cout << "✗ 圆柱形禁飞区点检查失败，期望 'test_cylinder'，得到 '" << result << "'" << std::endl;
        }
        
        // 测试多边形禁飞区内的点
        result = manager.is_point_inside_any_nfz_2d(2.0f, 2.0f);
        if (result == "test_polygon") {
            std::cout << "✓ 多边形禁飞区点检查成功" << std::endl;
        } else {
            std::cout << "✗ 多边形禁飞区点检查失败，期望 'test_polygon'，得到 '" << result << "'" << std::endl;
        }
        
        // 测试不在任何禁飞区内的点
        result = manager.is_point_inside_any_nfz_2d(9.0f, 9.0f);
        if (result.empty()) {
            std::cout << "✓ 非禁飞区点检查成功" << std::endl;
        } else {
            std::cout << "✗ 非禁飞区点检查失败，期望空字符串，得到 '" << result << "'" << std::endl;
        }
        
        // 测试获取类型信息
        const ObstacleTypeInfo& info = manager.get_type_info("test_cylinder");
        std::cout << "✓ 获取类型信息成功: " << info.description << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ 点检查功能测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "所有测试通过！最终编译修复成功。" << std::endl;
    return 0;
}
