2025-05-30 15:16:17,747 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:16:17,748 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:16:17,748 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:16:17,748 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:16:17,782 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:16:17,783 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:16:17,784 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:16:17,784 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:16:17,784 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:16:17,785 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:16:17,785 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:16:17,785 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:16:17,787 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:16:17,792 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:16:17,919 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:16:18,815 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:16:18,816 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:16:18,816 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:16:18,817 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:16:19,324 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:16:19,803 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:18:00,204 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:18:00,204 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:18:00,205 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:18:00,205 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:18:00,236 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:18:00,238 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:18:00,239 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:18:00,239 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:18:00,239 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:18:00,240 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:18:00,240 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:18:00,240 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:18:00,241 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:18:00,259 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:18:15,145 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:18:15,150 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:18:15,151 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:18:15,152 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:18:15,157 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:18:15,659 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:18:16,157 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:18:32,465 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:18:32,465 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:18:32,465 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:18:32,465 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:18:32,518 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:18:32,520 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:18:32,521 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:18:32,521 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:18:32,521 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:18:32,521 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:18:32,548 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:21:27,331 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:21:27,332 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:21:27,332 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:21:27,332 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:21:27,381 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:21:27,383 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:21:27,383 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:21:27,384 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:21:27,384 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:21:27,384 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:21:27,385 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:21:27,385 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:21:27,385 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:21:27,413 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:21:53,601 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:21:53,603 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:21:53,603 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:21:53,603 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:21:53,654 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:21:53,656 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:21:53,656 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:21:53,657 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:21:53,657 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:21:53,657 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:21:53,658 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:21:53,658 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:21:53,658 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:21:53,686 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:23:00,509 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:23:00,509 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:23:00,509 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:23:00,509 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:23:00,545 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:23:00,548 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:23:00,548 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:23:00,549 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:23:00,549 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:23:00,550 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:23:00,569 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:32:19,488 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:32:19,489 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:32:19,489 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:32:19,489 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:32:19,538 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:32:19,540 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:32:19,540 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:32:19,540 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:32:19,541 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:32:19,541 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:32:19,541 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:32:19,541 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:32:19,542 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:32:19,551 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:47:05,504 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:47:05,627 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:47:05,628 - kafka.client - WARNING - <BrokerConnection node_id=bootstrap-0 host=***********:9093 <connected> [IPv4 ('***********', 9093)]> timed out after 30000 ms. Closing connection.
2025-05-30 15:47:05,628 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:47:05,630 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:47:05,631 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:47:05,633 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:47:05,634 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:47:05,635 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:47:05,635 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:47:06,151 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:47:06,495 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:54:48,843 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:54:48,844 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:54:48,844 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:54:48,844 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:54:48,885 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:54:48,887 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:54:48,887 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:54:48,888 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:54:48,888 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:54:48,888 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:54:48,893 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:55:40,319 - kafka.client - WARNING - <BrokerConnection node_id=bootstrap-0 host=***********:9093 <connected> [IPv4 ('***********', 9093)]> timed out after 30000 ms. Closing connection.
2025-05-30 15:55:42,530 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 15:55:47,352 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 15:55:47,353 - src.handlers.message_handlers.base - ERROR - 处理禁飞区 测试 时出错: 'pathfinding_cpp.Map3D' object has no attribute 'add_solid_polygon_no_fly_zone'
2025-05-30 16:15:56,322 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:15:56,323 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:15:56,323 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:15:56,323 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:15:56,413 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:15:56,414 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:15:56,414 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:15:56,414 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:15:56,415 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:15:56,415 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:15:56,415 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:15:56,415 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:15:56,415 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:15:56,446 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:16:06,342 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:29:57,680 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:29:57,680 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:29:57,680 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:29:57,680 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:29:57,735 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:29:57,737 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:29:57,737 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:29:57,737 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:29:57,738 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:29:57,738 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:29:57,738 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:29:57,738 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:29:57,738 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:29:57,765 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:30:04,789 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:30:04,819 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:41:12,127 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:41:12,128 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:41:12,128 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:41:12,128 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:41:12,170 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:41:12,172 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:41:12,172 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:41:12,172 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:41:12,173 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:41:12,173 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:41:12,173 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:41:12,173 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:41:12,174 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:41:12,204 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:41:23,756 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:41:23,786 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:41:38,381 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:41:38,439 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:41:38,443 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 16:41:39,394 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 16:41:39,395 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 16:41:39,395 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:41:39,395 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 16:41:39,397 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 16:41:39,903 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 16:41:39,905 - src.handlers.message_handlers.base - INFO - Kafka生产者已关闭
2025-05-30 16:41:40,398 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 16:42:30,419 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:42:30,420 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:42:30,420 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:42:30,420 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:42:30,469 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:42:30,470 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:42:30,471 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:42:30,471 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:42:30,472 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:42:30,472 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:42:30,499 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:42:34,112 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:42:34,140 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:43:18,346 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:43:31,248 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:43:31,249 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 16:43:31,294 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 16:43:31,295 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 16:43:31,295 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:43:31,295 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:43:31,296 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 16:43:31,296 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 16:43:31,297 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 16:43:31,297 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 16:44:12,049 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:44:12,049 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:44:12,050 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:44:12,050 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:44:12,104 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:44:12,105 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:44:12,106 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:44:12,107 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:44:12,107 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:44:12,108 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:44:12,118 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:44:32,593 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:44:32,744 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:44:39,315 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:44:39,324 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:46:50,714 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:46:50,715 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:46:50,715 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:46:50,715 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:46:50,760 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:46:50,761 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:46:50,762 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:46:50,762 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:46:50,762 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:46:50,762 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:46:50,763 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:46:50,763 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:46:50,763 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:46:50,769 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:46:57,792 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:46:57,821 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:47:04,649 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:47:04,661 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:48:22,925 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-05-30 16:48:23,513 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-05-30 16:48:24,534 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-05-30 16:48:25,011 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-05-30 16:48:25,011 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-05-30 16:48:25,154 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-05-30 16:48:25,176 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-05-30 16:48:25,176 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-05-30 16:48:26,730 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-05-30 16:48:26,873 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-05-30 16:48:26,873 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-05-30 16:48:27,019 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-05-30 16:48:27,022 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-05-30 16:48:27,023 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-05-30 16:48:28,001 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-05-30 16:48:28,002 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-05-30 16:48:28,002 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-05-30 16:48:28,003 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-05-30 16:48:28,003 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-05-30 16:48:28,004 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-05-30 16:48:30,122 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-05-30 16:48:30,123 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-05-30 16:48:31,143 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-05-30 16:48:31,143 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-05-30 16:48:33,167 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 08:53:38,421 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 08:53:38,421 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 08:53:38,421 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 08:53:38,421 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 08:53:38,516 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 08:53:38,516 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 08:53:38,516 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 08:53:38,518 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 08:53:38,518 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 08:53:38,518 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 08:53:38,549 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 08:53:42,212 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 08:53:42,274 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 08:53:48,753 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 08:53:48,805 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 08:53:48,805 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 08:53:48,805 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 08:53:52,778 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 08:53:54,208 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 08:53:54,208 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 08:53:54,339 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 08:53:54,348 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 08:53:54,348 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 08:53:54,349 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 08:53:54,482 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 08:53:54,482 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 08:53:54,608 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 08:55:35,116 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 08:55:35,116 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 08:55:35,165 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748912194765, 'mode_code': 'RISK', 'data': {'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'flightapplyid': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 08:56:34', 'commWay': '', 'endTime': '2025-06-03 09:56:34', 'task_source': '0'}}[0m
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: 'pathfinding_cpp.GridConverter' object has no attribute 'height_to_relative'
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 0.0065 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0065 秒
2025-06-03 08:55:35,196 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '508a0fef-24e0-4b12-a9a9-0b73f9ccd873', 'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748912135, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: 'pathfinding_cpp.GridConverter' object has no attribute 'height_to_relative'"}, 'desc': '待调整'}[0m
2025-06-03 08:55:35,474 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 09:04:43,387 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:05:38,951 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:06:38,773 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:07:38,730 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:08:38,717 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:09:38,880 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:10:38,716 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:11:38,664 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:12:38,707 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:13:38,709 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:14:38,711 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:15:38,719 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:16:38,788 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:17:38,994 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:18:38,737 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:19:38,738 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:20:38,772 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:21:38,749 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:22:38,758 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:23:38,772 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:24:38,783 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:25:38,820 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:26:38,822 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:27:38,811 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:28:38,823 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:29:38,814 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:30:38,867 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:31:38,858 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:48:18,547 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 09:48:18,547 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 09:48:18,547 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 09:48:18,547 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 09:48:18,678 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 09:48:18,678 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 09:48:18,678 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 09:48:18,678 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 09:48:18,678 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 09:48:18,678 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 09:48:18,678 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 09:48:18,678 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 09:48:18,678 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 09:48:18,705 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 09:48:21,323 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 09:48:22,147 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 09:48:28,677 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 09:48:28,721 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 09:48:28,721 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 09:48:28,721 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 09:48:28,721 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 09:48:29,189 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 09:48:29,189 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 09:48:29,345 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 09:48:29,363 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 09:48:29,363 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 09:48:29,363 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 09:48:29,500 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 09:48:29,500 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 09:48:29,639 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 09:48:29,639 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 09:48:29,639 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 09:48:29,639 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 09:48:29,647 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 09:48:29,647 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 09:48:29,647 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 09:48:49,638 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 09:48:49,638 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 09:48:49,971 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748915388371, 'mode_code': 'RISK', 'data': {'id': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'flightapplyid': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 09:49:48', 'commWay': '', 'endTime': '2025-06-03 10:49:48', 'task_source': '0'}}[0m
2025-06-03 09:48:49,983 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 09:48:49,983 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:01:45,015 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:01:45,016 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:01:45,016 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:01:45,016 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:01:45,065 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:01:45,066 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:01:45,066 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:01:45,067 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:01:45,067 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:01:45,067 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:01:45,068 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:01:45,068 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:01:45,068 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:01:45,096 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:02:20,356 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:02:20,388 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:02:27,343 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:02:27,352 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:02:27,353 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:02:27,353 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:02:27,354 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:02:27,747 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:02:27,748 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:02:27,876 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:02:27,887 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:02:27,887 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:02:27,888 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:02:28,017 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:02:28,017 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:02:28,141 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:02:28,143 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:02:28,144 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:02:28,144 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:02:28,144 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:02:28,145 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:02:28,145 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:02:28,145 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:02:28,145 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:02:28,146 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:02:28,146 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:02:28,150 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:02:28,150 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:02:28,157 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:02:30,379 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:02:30,379 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:02:30,421 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748915388371, 'mode_code': 'RISK', 'data': {'id': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'flightapplyid': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 09:49:48', 'commWay': '', 'endTime': '2025-06-03 10:49:48', 'task_source': '0'}}[0m
2025-06-03 10:02:30,426 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:02:30,426 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0042 秒
2025-06-03 10:02:30,460 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'f0285f7f-4cda-4f7e-ad61-1a817d4fd026', 'id': 'fb6d7d74-a42a-4af3-b4bc-8092bfc71cc2', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748916150, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 10:02:30,728 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:03:07,406 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:03:07,406 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:03:07,406 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748916247291, 'mode_code': 'RISK', 'data': {'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'flightapplyid': '92447e49-5c31-417c-a01f-6046b3a289ff', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:04:07', 'commWay': '', 'endTime': '2025-06-03 11:04:07', 'task_source': '0'}}[0m
2025-06-03 10:03:07,406 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:03:07,406 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:05:27,125 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 139.7102 秒
2025-06-03 10:05:27,126 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 139.7208 秒
2025-06-03 10:05:27,142 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '42b77ffc-c4bc-4f4b-a1e5-2d9eabe77ea8', 'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748916327, 'data': {'risk_state': False, 'risk_reason': '起飞或降落点不在地图范围内'}, 'desc': '待调整'}[0m
2025-06-03 10:05:27,148 - src.handlers.risk_assessment_consumer - WARNING - 状态机消费者提交偏移量失败: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:05:27,148 - kafka.coordinator.consumer - ERROR - Offset commit failed: This is likely to cause duplicate message delivery
Traceback (most recent call last):
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 528, in _maybe_auto_commit_offsets_sync
    self.commit_offsets_sync(self._subscription.all_consumed_offsets())
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 521, in commit_offsets_sync
    raise future.exception # pylint: disable-msg=raising-bad-type
kafka.errors.CommitFailedError: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:05:27,259 - kafka.coordinator - WARNING - Heartbeat session expired, marking coordinator dead
2025-06-03 10:05:27,259 - kafka.coordinator - WARNING - Marking the coordinator dead (node coordinator-1001) for group route_planning_group-flight-monitor: Heartbeat session expired.
2025-06-03 10:05:27,415 - kafka.coordinator.consumer - WARNING - Auto offset commit failed for group route_planning_group-flight-monitor: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:05:27,415 - kafka.coordinator.consumer - ERROR - Offset commit failed: This is likely to cause duplicate message delivery
Traceback (most recent call last):
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 528, in _maybe_auto_commit_offsets_sync
    self.commit_offsets_sync(self._subscription.all_consumed_offsets())
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 521, in commit_offsets_sync
    raise future.exception # pylint: disable-msg=raising-bad-type
kafka.errors.CommitFailedError: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:05:27,796 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.000秒
2025-06-03 10:05:27,796 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:05:27,796 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:05:27,796 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748916247291, 'mode_code': 'RISK', 'data': {'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'flightapplyid': '92447e49-5c31-417c-a01f-6046b3a289ff', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:04:07', 'commWay': '', 'endTime': '2025-06-03 11:04:07', 'task_source': '0'}}[0m
2025-06-03 10:05:27,796 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:05:27,796 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0000 秒
2025-06-03 10:05:27,814 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '1278b547-2b5e-4eb0-8c46-eca83a0eaae8', 'id': '92447e49-5c31-417c-a01f-6046b3a289ff', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748916327, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 10:05:27,814 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.018秒，提交耗时 0.000秒
2025-06-03 10:05:27,953 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:05:36,238 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:05:36,238 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:05:36,241 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748916396025, 'mode_code': 'RISK', 'data': {'id': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'flightapplyid': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:06:36', 'commWay': '', 'endTime': '2025-06-03 11:06:36', 'task_source': '0'}}[0m
2025-06-03 10:05:36,241 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:05:36,241 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:23:28,827 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:23:28,827 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:23:28,827 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:23:28,827 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:23:28,881 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:23:28,889 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:23:28,889 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:23:28,889 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:23:28,889 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:23:28,889 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:23:28,889 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:23:28,889 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:23:28,889 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:23:28,917 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:23:32,315 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:23:32,351 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:23:38,887 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:23:38,906 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:23:38,907 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:23:38,907 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:23:38,908 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:23:39,326 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:23:39,326 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:23:39,470 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:23:39,482 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:23:39,482 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:23:39,482 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:23:39,605 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:23:39,605 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:23:39,745 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:23:39,745 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:23:39,745 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:23:39,745 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:23:42,054 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:23:42,054 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:23:42,089 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748916396025, 'mode_code': 'RISK', 'data': {'id': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'flightapplyid': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:06:36', 'commWay': '', 'endTime': '2025-06-03 11:06:36', 'task_source': '0'}}[0m
2025-06-03 10:23:42,089 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:23:42,097 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0084 秒
2025-06-03 10:23:42,124 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '688d26da-d49a-4828-b8cf-ca125834c231', 'id': 'adc2fc9e-5eb2-4490-8d2d-3da9f9bc9111', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917422, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 10:23:42,390 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:23:50,658 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:23:50,658 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:23:50,664 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917490392, 'mode_code': 'RISK', 'data': {'id': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'flightapplyid': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:24:50', 'commWay': '', 'endTime': '2025-06-03 11:24:50', 'task_source': '0'}}[0m
2025-06-03 10:23:50,665 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:23:50,665 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:25:21,092 - kafka.coordinator - WARNING - Heartbeat: local member_id was not recognized; this consumer needs to re-join
2025-06-03 10:25:21,269 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 90.6031 秒
2025-06-03 10:25:21,269 - kafka.coordinator.consumer - ERROR - Offset commit failed: This is likely to cause duplicate message delivery
Traceback (most recent call last):
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 528, in _maybe_auto_commit_offsets_sync
    self.commit_offsets_sync(self._subscription.all_consumed_offsets())
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 521, in commit_offsets_sync
    raise future.exception # pylint: disable-msg=raising-bad-type
kafka.errors.CommitFailedError: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:25:21,269 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 90.6041 秒
2025-06-03 10:25:21,269 - kafka.coordinator - WARNING - Heartbeat session expired, marking coordinator dead
2025-06-03 10:25:21,269 - kafka.coordinator - WARNING - Marking the coordinator dead (node coordinator-1001) for group route_planning_group-state-machine: Heartbeat session expired.
2025-06-03 10:25:21,286 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '91c8d4d3-7074-4971-93c6-3e9984cd567a', 'id': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917521, 'data': {'risk_state': False, 'risk_reason': '起飞或降落点不在地图范围内'}, 'desc': '待调整'}[0m
2025-06-03 10:25:21,298 - src.handlers.risk_assessment_consumer - WARNING - 状态机消费者提交偏移量失败: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:25:21,298 - kafka.coordinator.consumer - ERROR - Offset commit failed: This is likely to cause duplicate message delivery
Traceback (most recent call last):
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 528, in _maybe_auto_commit_offsets_sync
    self.commit_offsets_sync(self._subscription.all_consumed_offsets())
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 521, in commit_offsets_sync
    raise future.exception # pylint: disable-msg=raising-bad-type
kafka.errors.CommitFailedError: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:25:21,322 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.024秒
2025-06-03 10:25:21,322 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:25:21,322 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:25:21,336 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917521, 'desc': '待调整', 'mode_code': 'ADJUST', 'data': {'id': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'flightapplyid': 'd666293c-3e2d-42a9-af3e-5567d4d7d07d', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:24:50', 'commWay': '', 'endTime': '2025-06-03 11:24:50', 'task_source': '0', 'risk_state': False, 'risk_reason': '起飞或降落点不在地图范围内'}}[0m
2025-06-03 10:25:21,336 - src.handlers.risk_assessment_consumer - ERROR - Task d666293c-3e2d-42a9-af3e-5567d4d7d07d is not a risk assessment task
2025-06-03 10:25:21,341 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.019秒，提交耗时 0.005秒
2025-06-03 10:25:29,216 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:25:29,231 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:25:29,232 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '3538d603-a00c-43c7-9205-1edff202db4b', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917589013, 'mode_code': 'RISK', 'data': {'id': '3538d603-a00c-43c7-9205-1edff202db4b', 'flightapplyid': '3538d603-a00c-43c7-9205-1edff202db4b', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:26:29', 'commWay': '', 'endTime': '2025-06-03 11:26:29', 'task_source': '0'}}[0m
2025-06-03 10:25:29,232 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:25:29,232 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:26:07,039 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:26:07,040 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:26:07,040 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:26:07,041 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:26:07,087 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:26:07,087 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:26:07,087 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:26:07,087 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:26:07,087 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:26:07,087 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:26:07,087 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:26:07,087 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:26:07,087 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:26:07,096 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:26:48,543 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:26:48,595 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:26:55,112 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:26:55,137 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:26:55,138 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:26:55,138 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:26:55,139 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:26:55,560 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:26:55,560 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:26:55,692 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:26:55,706 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:26:55,706 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:26:55,706 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:26:55,817 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:26:55,817 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:26:55,937 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:26:55,951 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:26:55,951 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:26:55,951 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:26:55,951 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:26:55,951 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:26:55,951 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:26:55,951 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:26:55,951 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:26:55,959 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:26:55,959 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:26:55,961 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:26:55,961 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:26:55,962 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:26:58,181 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:26:58,181 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:26:58,208 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '3538d603-a00c-43c7-9205-1edff202db4b', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917589013, 'mode_code': 'RISK', 'data': {'id': '3538d603-a00c-43c7-9205-1edff202db4b', 'flightapplyid': '3538d603-a00c-43c7-9205-1edff202db4b', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:26:29', 'commWay': '', 'endTime': '2025-06-03 11:26:29', 'task_source': '0'}}[0m
2025-06-03 10:26:58,213 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:26:58,213 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0053 秒
2025-06-03 10:26:58,231 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '5eb51f9e-d9b0-42f9-ba76-649a9dfd3764', 'id': '3538d603-a00c-43c7-9205-1edff202db4b', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917618, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 10:26:58,391 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:27:05,085 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:27:05,087 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:27:05,109 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917684938, 'mode_code': 'RISK', 'data': {'id': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'flightapplyid': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:28:04', 'commWay': '', 'endTime': '2025-06-03 11:28:04', 'task_source': '0'}}[0m
2025-06-03 10:27:05,111 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:27:05,111 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:28:20,418 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:28:20,428 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:28:20,428 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:28:20,428 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:28:20,485 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:28:20,485 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:28:20,485 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:28:20,485 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:28:20,485 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:28:20,485 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:28:20,485 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:28:20,489 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:28:20,489 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:28:20,511 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:28:23,427 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:28:23,495 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:28:30,083 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:28:30,118 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:28:30,127 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:28:30,127 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:28:30,127 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:28:30,552 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:28:30,552 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:28:30,690 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:28:30,692 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:28:30,692 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:28:30,692 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:28:30,815 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:28:30,815 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:28:30,951 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:28:30,951 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:28:30,951 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:28:30,951 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:28:30,951 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:28:30,951 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:28:30,966 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:28:30,966 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:28:30,966 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:28:30,966 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:28:30,966 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:28:30,971 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:28:30,971 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:28:30,975 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:28:33,410 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:28:33,410 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:28:33,452 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917684938, 'mode_code': 'RISK', 'data': {'id': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'flightapplyid': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:28:04', 'commWay': '', 'endTime': '2025-06-03 11:28:04', 'task_source': '0'}}[0m
2025-06-03 10:28:33,457 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:28:33,457 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0053 秒
2025-06-03 10:28:33,499 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'c21a6262-fcb6-4e78-b036-342ab82e4f59', 'id': 'ec91897b-cc9a-4d52-bd97-0020ae18da08', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917713, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 10:28:33,769 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:28:37,409 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:28:37,421 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:28:37,421 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748917777233, 'mode_code': 'RISK', 'data': {'id': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'flightapplyid': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:29:37', 'commWay': '', 'endTime': '2025-06-03 11:29:37', 'task_source': '0'}}[0m
2025-06-03 10:28:37,421 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:28:37,421 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:28:58,582 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 20.2470 秒
2025-06-03 10:29:44,116 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 24.6511 秒
2025-06-03 10:30:11,782 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:
    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict

Invoked with: <pathfinding_cpp.JPS object at 0x00000265FBE25CB0>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72.0, agent_id='b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', start_time=1748917777, occupancy_map=<pathfinding_cpp.OccupancyMap object at 0x00000265FBCC7B30>
2025-06-03 10:30:11,783 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 94.3622 秒
2025-06-03 10:30:11,783 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 94.3622 秒
2025-06-03 10:30:11,940 - kafka.coordinator - WARNING - Heartbeat session expired, marking coordinator dead
2025-06-03 10:30:11,940 - kafka.coordinator - WARNING - Marking the coordinator dead (node coordinator-1001) for group route_planning_group-state-machine: Heartbeat session expired.
2025-06-03 10:30:11,965 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '0472bd5f-a357-4940-a62c-2a8586fcabda', 'id': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917811, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:\n    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict\n\nInvoked with: <pathfinding_cpp.JPS object at 0x00000265FBE25CB0>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72.0, agent_id='b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', start_time=1748917777, occupancy_map=<pathfinding_cpp.OccupancyMap object at 0x00000265FBCC7B30>"}, 'desc': '待调整'}[0m
2025-06-03 10:30:11,993 - src.handlers.risk_assessment_consumer - WARNING - 状态机消费者提交偏移量失败: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:30:11,993 - kafka.coordinator.consumer - ERROR - Offset commit failed: This is likely to cause duplicate message delivery
Traceback (most recent call last):
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 528, in _maybe_auto_commit_offsets_sync
    self.commit_offsets_sync(self._subscription.all_consumed_offsets())
  File "c:\Users\<USER>\miniconda3\envs\py39\lib\site-packages\kafka\coordinator\consumer.py", line 521, in commit_offsets_sync
    raise future.exception # pylint: disable-msg=raising-bad-type
kafka.errors.CommitFailedError: CommitFailedError: Commit cannot be completed since the group has already
            rebalanced and assigned the partitions to another member.
            This means that the time between subsequent calls to poll()
            was longer than the configured max_poll_interval_ms, which
            typically implies that the poll loop is spending too much
            time message processing. You can address this either by
            increasing the rebalance timeout with max_poll_interval_ms,
            or by reducing the maximum size of batches returned in poll()
            with max_poll_records.
            
2025-06-03 10:30:12,051 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.058秒
2025-06-03 10:30:12,052 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:30:12,052 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:30:12,075 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748917811, 'desc': '待调整', 'mode_code': 'ADJUST', 'data': {'id': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'flightapplyid': 'b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:29:37', 'commWay': '', 'endTime': '2025-06-03 11:29:37', 'task_source': '0', 'risk_state': False, 'risk_reason': "处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:\n    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict\n\nInvoked with: <pathfinding_cpp.JPS object at 0x00000265FBE25CB0>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72.0, agent_id='b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1', start_time=1748917777, occupancy_map=<pathfinding_cpp.OccupancyMap object at 0x00000265FBCC7B30>"}}[0m
2025-06-03 10:30:12,076 - src.handlers.risk_assessment_consumer - ERROR - Task b17b5d8f-e3c3-4eda-8f50-47cc9b9f14b1 is not a risk assessment task
2025-06-03 10:30:12,091 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.039秒，提交耗时 0.015秒
2025-06-03 10:33:17,440 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:33:17,440 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:33:17,440 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:33:17,440 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:33:17,497 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:33:17,497 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:33:17,497 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:33:17,497 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:33:17,497 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:33:17,497 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:33:17,497 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:33:17,497 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:33:17,497 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:33:17,527 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:33:22,582 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:33:22,612 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:33:29,069 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:33:29,110 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:33:29,110 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:33:29,110 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:33:29,110 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:33:29,543 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:33:29,543 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:33:29,652 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:33:29,674 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:33:29,674 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:33:29,674 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:33:29,788 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:33:29,788 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:33:29,911 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:33:29,920 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:33:29,920 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:33:29,920 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:33:29,920 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:33:29,927 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:33:29,927 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:33:29,933 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:34:14,625 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:34:14,625 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:34:14,655 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'b5e7d944-2f84-43ae-832a-4ec767e75e5b', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748918076079, 'mode_code': 'RISK', 'data': {'id': 'b5e7d944-2f84-43ae-832a-4ec767e75e5b', 'flightapplyid': 'b5e7d944-2f84-43ae-832a-4ec767e75e5b', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:34:36', 'commWay': '', 'endTime': '2025-06-03 11:34:36', 'task_source': '0'}}[0m
2025-06-03 10:34:14,664 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:34:14,664 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:34:14,664 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 10:34:14,664 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 10:35:06,730 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:
    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict

Invoked with: <pathfinding_cpp.JPS object at 0x0000020764D56270>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72, agent_id='b5e7d944-2f84-43ae-832a-4ec767e75e5b', start_time=1748918076, occupancy_map=<pathfinding_cpp.OccupancyMap object at 0x0000020764CB85B0>
2025-06-03 10:35:06,731 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 52.0757 秒
2025-06-03 10:35:06,731 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 52.0757 秒
2025-06-03 10:35:06,766 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '901d6cfb-4dad-48e1-b99a-d3575f7edbeb', 'id': 'b5e7d944-2f84-43ae-832a-4ec767e75e5b', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748918106, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:\n    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict\n\nInvoked with: <pathfinding_cpp.JPS object at 0x0000020764D56270>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72, agent_id='b5e7d944-2f84-43ae-832a-4ec767e75e5b', start_time=1748918076, occupancy_map=<pathfinding_cpp.OccupancyMap object at 0x0000020764CB85B0>"}, 'desc': '待调整'}[0m
2025-06-03 10:35:07,029 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:36:53,851 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:36:53,851 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:36:53,851 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:36:53,851 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:36:53,893 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:36:53,894 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:36:53,895 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:36:53,895 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:36:53,895 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:36:53,895 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:36:53,895 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:36:53,895 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:36:53,895 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:36:53,913 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:37:03,401 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:37:03,436 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:37:09,808 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:37:09,827 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:37:09,827 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:37:09,828 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:37:09,829 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:37:10,258 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:37:10,258 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:37:10,398 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:37:10,420 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:37:10,420 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:37:10,420 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:37:10,558 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:37:10,558 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:37:10,690 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:37:10,697 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:37:10,697 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:37:10,697 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:37:10,697 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:37:48,933 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:37:48,933 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:37:48,969 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748918295510, 'mode_code': 'RISK', 'data': {'id': '96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', 'flightapplyid': '96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:38:15', 'commWay': '', 'endTime': '2025-06-03 11:38:15', 'task_source': '0'}}[0m
2025-06-03 10:37:48,974 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:37:48,974 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:37:48,974 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 10:37:48,976 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 10:37:59,843 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:
    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict

Invoked with: <pathfinding_cpp.JPS object at 0x0000025076A38130>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72.0, agent_id='96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', start_time=1748918295
2025-06-03 10:37:59,843 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 10.8740 秒
2025-06-03 10:37:59,843 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 10.8740 秒
2025-06-03 10:37:59,869 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '9cc4d226-604d-4485-bb81-54a07e42f66d', 'id': '96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748918279, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: find_path(): incompatible function arguments. The following argument types are supported:\n    1. (self: pathfinding_cpp.JPS, start: tuple, goal: tuple, min_height: int, agent_id: str, start_time: int) -> dict\n\nInvoked with: <pathfinding_cpp.JPS object at 0x0000025076A38130>; kwargs: start=(1137.0, 3935.0, 30.0), goal=(23.0, 2245.0, 30.0), min_height=72.0, agent_id='96f8032d-1b9d-4b74-abe6-8dc6f5485ff0', start_time=1748918295"}, 'desc': '待调整'}[0m
2025-06-03 10:38:00,127 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:39:25,489 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 10:39:25,489 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 10:39:25,489 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 10:39:25,489 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 10:39:26,550 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 10:39:26,550 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 10:39:26,550 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 10:39:26,550 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 10:39:26,550 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 10:39:26,550 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 10:39:26,550 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 10:39:26,550 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 10:39:26,550 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 10:39:26,586 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 10:39:30,025 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 10:39:30,249 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 10:39:37,039 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 10:39:37,068 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 10:39:37,070 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 10:39:37,071 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 10:39:37,071 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 10:39:37,520 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 10:39:37,520 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 10:39:37,638 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 10:39:37,654 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 10:39:37,654 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 10:39:37,654 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 10:39:37,801 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 10:39:37,801 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 10:39:37,963 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 10:39:37,963 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 10:39:37,963 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 10:39:37,963 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 10:39:37,970 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 10:39:37,970 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 10:39:37,973 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 10:39:37,973 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 10:39:37,980 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 10:40:21,282 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 10:40:21,282 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 10:40:21,344 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '3a65836b-da22-4139-ace7-0535605992cc', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748918443143, 'mode_code': 'RISK', 'data': {'id': '3a65836b-da22-4139-ace7-0535605992cc', 'flightapplyid': '3a65836b-da22-4139-ace7-0535605992cc', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:40:43', 'commWay': '', 'endTime': '2025-06-03 11:40:43', 'task_source': '0'}}[0m
2025-06-03 10:40:21,349 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 10:40:21,349 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 10:40:21,349 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 10:40:21,349 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 10:41:09,298 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 34.4959 秒
2025-06-03 11:12:27,200 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:12:27,201 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:12:27,201 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:12:27,201 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:12:27,241 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:12:27,241 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:12:27,241 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:12:27,241 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:12:27,241 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:12:27,241 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:12:27,245 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:12:27,245 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:12:27,245 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:12:27,257 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:12:27,584 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:12:27,628 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:12:34,221 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:12:34,256 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:12:34,256 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:12:34,256 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:12:34,256 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:12:34,681 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:12:34,681 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:12:34,808 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:12:34,830 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:12:34,830 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:12:34,830 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:12:34,952 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:12:34,952 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:12:35,098 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:12:35,098 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:12:35,098 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:12:35,098 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:12:35,106 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:12:35,107 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:12:35,109 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:12:35,109 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:12:35,112 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:12:36,942 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:12:36,942 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:12:36,994 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '3a65836b-da22-4139-ace7-0535605992cc', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748918443143, 'mode_code': 'RISK', 'data': {'id': '3a65836b-da22-4139-ace7-0535605992cc', 'flightapplyid': '3a65836b-da22-4139-ace7-0535605992cc', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 10:40:43', 'commWay': '', 'endTime': '2025-06-03 11:40:43', 'task_source': '0'}}[0m
2025-06-03 11:12:36,998 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:12:36,998 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0031 秒
2025-06-03 11:12:37,037 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '91659feb-f4eb-45b3-af07-0088310af97e', 'id': '3a65836b-da22-4139-ace7-0535605992cc', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748920356, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 11:12:37,311 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:12:49,520 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:12:49,520 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:12:49,536 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '97dca691-111d-48a0-8bbe-27d0d564cd86', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748920429288, 'mode_code': 'RISK', 'data': {'id': '97dca691-111d-48a0-8bbe-27d0d564cd86', 'flightapplyid': '97dca691-111d-48a0-8bbe-27d0d564cd86', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:13:49', 'commWay': '', 'endTime': '2025-06-03 12:13:49', 'task_source': '0'}}[0m
2025-06-03 11:12:49,536 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:12:49,536 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 11:12:49,536 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 11:12:49,542 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 11:13:01,396 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 5.8726 秒
2025-06-03 11:13:35,209 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: 'tuple' object has no attribute 't'
2025-06-03 11:13:35,211 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 45.6744 秒
2025-06-03 11:13:35,211 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 45.6751 秒
2025-06-03 11:13:35,252 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '8d938b74-d058-41a1-8e32-0e6f40e9d4b3', 'id': '97dca691-111d-48a0-8bbe-27d0d564cd86', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748920415, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: 'tuple' object has no attribute 't'"}, 'desc': '待调整'}[0m
2025-06-03 11:13:35,513 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:14:16,740 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:14:16,740 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:14:16,740 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:14:16,740 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:14:16,787 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:14:16,787 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:14:16,793 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:14:16,794 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:14:16,794 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:14:16,794 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:14:16,794 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:14:16,794 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:14:16,794 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:14:16,819 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:14:17,142 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:14:17,216 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:14:23,695 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:14:23,727 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:14:23,727 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:14:23,735 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:14:23,735 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:14:24,200 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:14:24,200 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:14:24,403 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:14:24,403 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:14:24,403 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:14:24,403 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:14:24,554 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:14:24,554 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:14:24,760 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:14:24,760 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:14:24,760 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:14:24,760 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:14:24,776 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:14:24,776 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:14:24,776 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:14:24,776 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:14:24,776 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:15:12,429 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:15:12,430 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:15:12,499 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '5b7e0c6c-a1db-4a62-9f0c-f8f311bb7cae', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748920552241, 'mode_code': 'RISK', 'data': {'id': '5b7e0c6c-a1db-4a62-9f0c-f8f311bb7cae', 'flightapplyid': '5b7e0c6c-a1db-4a62-9f0c-f8f311bb7cae', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:15:52', 'commWay': '', 'endTime': '2025-06-03 12:15:52', 'task_source': '0'}}[0m
2025-06-03 11:15:12,504 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:15:12,505 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 11:15:12,505 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0006 秒
2025-06-03 11:15:12,505 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 11:15:16,693 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0010 秒
2025-06-03 11:15:49,571 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 17.6557 秒
2025-06-03 11:15:49,572 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: 'tuple' object has no attribute 't'
2025-06-03 11:15:49,573 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 37.0732 秒
2025-06-03 11:15:49,573 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 37.0732 秒
2025-06-03 11:15:49,677 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'c58684c1-1f69-4e8c-86e7-45b9c904a37c', 'id': '5b7e0c6c-a1db-4a62-9f0c-f8f311bb7cae', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748920549, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: 'tuple' object has no attribute 't'"}, 'desc': '待调整'}[0m
2025-06-03 11:15:49,941 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:16:40,349 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:16:40,350 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:16:40,350 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:16:40,350 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:16:40,397 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:16:40,399 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:16:40,399 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:16:40,400 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:16:40,400 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:16:40,401 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:16:40,401 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:16:40,401 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:16:40,402 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:16:40,419 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:16:40,757 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:16:40,801 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:16:47,694 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:16:47,704 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:16:47,705 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:16:47,706 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:16:47,706 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:16:48,126 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:16:48,126 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:16:48,267 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:16:48,282 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:16:48,282 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:16:48,284 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:16:48,401 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:16:48,402 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:16:48,522 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:16:48,524 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:16:48,524 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:16:48,524 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:16:48,524 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:16:48,525 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:16:48,525 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:16:48,525 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:16:48,525 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:16:48,526 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:16:48,526 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:16:48,527 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:16:48,527 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:16:48,528 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:17:36,066 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:17:36,066 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:17:36,090 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'c3889e9c-3af9-46ac-a947-8df2a87118e4', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748920677177, 'mode_code': 'RISK', 'data': {'id': 'c3889e9c-3af9-46ac-a947-8df2a87118e4', 'flightapplyid': 'c3889e9c-3af9-46ac-a947-8df2a87118e4', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:17:57', 'commWay': '', 'endTime': '2025-06-03 12:17:57', 'task_source': '0'}}[0m
2025-06-03 11:17:36,095 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:17:36,096 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 11:17:36,096 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 11:17:36,096 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 11:18:00,038 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0010 秒
2025-06-03 11:18:13,658 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 10.6604 秒
2025-06-03 11:18:13,659 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: 'tuple' object has no attribute 't'
2025-06-03 11:18:13,659 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 37.5685 秒
2025-06-03 11:18:13,659 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 37.5685 秒
2025-06-03 11:18:13,694 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'eff04928-9674-40e7-acc0-e1aee6a7d4d7', 'id': 'c3889e9c-3af9-46ac-a947-8df2a87118e4', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748920693, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: 'tuple' object has no attribute 't'"}, 'desc': '待调整'}[0m
2025-06-03 11:18:13,968 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:18:35,820 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:18:35,821 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:18:35,821 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:18:35,821 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:18:35,888 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:18:35,889 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:18:35,890 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:18:35,891 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:18:35,891 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:18:35,891 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:18:35,892 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:18:35,892 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:18:35,892 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:18:35,902 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:18:36,243 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:18:36,285 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:18:42,840 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:18:42,852 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:18:42,854 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:18:42,855 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:18:42,855 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:18:43,266 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:18:43,266 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:18:43,414 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:18:43,432 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:18:43,432 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:18:43,433 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:18:43,568 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:18:43,569 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:18:43,711 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:18:43,713 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:18:43,713 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:18:43,713 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:18:43,714 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:18:43,714 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:18:43,714 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:18:43,714 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:18:43,714 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:18:43,715 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:18:43,715 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:18:43,716 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:18:43,717 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:18:43,718 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:19:31,514 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:19:31,515 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:19:31,572 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '1419946c-9691-42a6-9d6d-f7f37dd843c3', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748920787975, 'mode_code': 'RISK', 'data': {'id': '1419946c-9691-42a6-9d6d-f7f37dd843c3', 'flightapplyid': '1419946c-9691-42a6-9d6d-f7f37dd843c3', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:19:47', 'commWay': '', 'endTime': '2025-06-03 12:19:47', 'task_source': '0'}}[0m
2025-06-03 11:19:31,577 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:19:31,577 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 11:19:31,578 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 11:19:31,578 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 11:19:39,112 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0006 秒
2025-06-03 11:19:44,909 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 11:20:14,879 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0030 秒
2025-06-03 11:20:20,053 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 11:20:20,055 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 11:20:20,055 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 11:20:20,056 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 11:20:20,057 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: add_path(): incompatible function arguments. The following argument types are supported:
    1. (self: pathfinding_cpp.OccupancyMap, flight_id: str, path: list) -> None

Invoked with: <pathfinding_cpp.OccupancyMap object at 0x0000025A60E601B0>, [(1137.0, 3935.0, 30.0, 1748920787), (1137.0, 3935.0, 35.0, 1748920812), (1137.0, 3935.0, 40.0, 1748920837), (1137.0, 3935.0, 45.0, 1748920862), (1137.0, 3935.0, 50.0, 1748920887), (1137.0, 3935.0, 55.0, 1748920912), (1137.0, 3935.0, 60.0, 1748920937), (1137.0, 3935.0, 65.0, 1748920962), (1137.0, 3935.0, 70.0, 1748920987), (1137.0, 3935.0, 72.0, 1748920997), (1133.7041015625, 3930.0, 72.0, 1748920998), (1130.4083251953125, 3925.0, 72.0, 1748920999), (1127.1124267578125, 3920.0, 72.0, 1748921000), (1123.8165283203125, 3915.0, 72.0, 1748921001), (1120.520751953125, 3910.0, 72.0, 1748921002), (1117.224853515625, 3905.0, 72.0, 1748921003), (1113.928955078125, 3900.0, 72.0, 1748921004), (1110.6331787109375, 3895.0, 72.0, 1748921005), (1107.3372802734375, 3890.0, 72.0, 1748921006), (1104.0413818359375, 3885.0, 72.0, 1748921007), (1100.74560546875, 3880.0, 72.0, 1748921008), (1097.44970703125, 3875.0, 72.0, 1748921009), (1094.15380859375, 3870.0, 72.0, 1748921010), (1090.8580322265625, 3865.0, 72.0, 1748921011), (1087.5621337890625, 3860.0, 72.0, 1748921012), (1084.2662353515625, 3855.0, 72.0, 1748921013), (1080.970458984375, 3850.0, 72.0, 1748921014), (1077.674560546875, 3845.0, 72.0, 1748921015), (1074.378662109375, 3840.0, 72.0, 1748921016), (1071.0828857421875, 3835.0, 72.0, 1748921017), (1067.7869873046875, 3830.0, 72.0, 1748921018), (1064.4910888671875, 3825.0, 72.0, 1748921019), (1061.1953125, 3820.0, 72.0, 1748921020), (1057.8994140625, 3815.0, 72.0, 1748921021), (1054.603515625, 3810.0, 72.0, 1748921022), (1051.3077392578125, 3805.0, 72.0, 1748921023), (1048.0118408203125, 3800.0, 72.0, 1748921024), (1044.7159423828125, 3795.0, 72.0, 1748921025), (1041.420166015625, 3790.0, 72.0, 1748921026), (1038.124267578125, 3785.0, 72.0, 1748921027), (1034.828369140625, 3780.0, 72.0, 1748921028), (1031.5325927734375, 3775.0, 72.0, 1748921029), (1028.2366943359375, 3770.0, 72.0, 1748921030), (1024.9407958984375, 3765.0, 72.0, 1748921031), (1021.6449584960938, 3760.0, 72.0, 1748921032), (1018.34912109375, 3755.0, 72.0, 1748921033), (1015.0532836914062, 3750.0, 72.0, 1748921034), (1011.7573852539062, 3745.0, 72.0, 1748921035), (1008.4615478515625, 3740.0, 72.0, 1748921036), (1005.1657104492188, 3735.0, 72.0, 1748921037), (1001.8698120117188, 3730.0, 72.0, 1748921038), (998.573974609375, 3725.0, 72.0, 1748921039), (995.278076171875, 3720.0, 72.0, 1748921040), (991.9822387695312, 3715.0, 72.0, 1748921041), (988.6864013671875, 3710.0, 72.0, 1748921042), (985.3905029296875, 3705.0, 72.0, 1748921043), (982.0946655273438, 3700.0, 72.0, 1748921044), (978.798828125, 3695.0, 72.0, 1748921045), (975.5029296875, 3690.0, 72.0, 1748921046), (972.2070922851562, 3685.0, 72.0, 1748921047), (968.9112548828125, 3680.0, 72.0, 1748921048), (965.6153564453125, 3675.0, 72.0, 1748921049), (962.3195190429688, 3670.0, 72.0, 1748921050), (959.023681640625, 3665.0, 72.0, 1748921051), (955.727783203125, 3660.0, 72.0, 1748921052), (952.4319458007812, 3655.0, 72.0, 1748921053), (949.1361083984375, 3650.0, 72.0, 1748921054), (945.8402099609375, 3645.0, 72.0, 1748921055), (942.5443725585938, 3640.0, 72.0, 1748921056), (939.24853515625, 3635.0, 72.0, 1748921057), (935.95263671875, 3630.0, 72.0, 1748921058), (932.6567993164062, 3625.0, 72.0, 1748921059), (929.3609619140625, 3620.0, 72.0, 1748921060), (926.0650634765625, 3615.0, 72.0, 1748921061), (922.7692260742188, 3610.0, 72.0, 1748921062), (919.473388671875, 3605.0, 72.0, 1748921063), (916.177490234375, 3600.0, 72.0, 1748921064), (912.8816528320312, 3595.0, 72.0, 1748921065), (909.5858154296875, 3590.0, 72.0, 1748921066), (906.2899169921875, 3585.0, 72.0, 1748921067), (902.9940795898438, 3580.0, 72.0, 1748921068), (899.6982421875, 3575.0, 72.0, 1748921069), (896.40234375, 3570.0, 72.0, 1748921070), (893.1065063476562, 3565.0, 72.0, 1748921071), (889.8106689453125, 3560.0, 72.0, 1748921072), (886.5147705078125, 3555.0, 72.0, 1748921073), (883.2189331054688, 3550.0, 72.0, 1748921074), (879.923095703125, 3545.0, 72.0, 1748921075), (876.627197265625, 3540.0, 72.0, 1748921076), (873.3313598632812, 3535.0, 72.0, 1748921077), (870.0355224609375, 3530.0, 72.0, 1748921078), (866.7396240234375, 3525.0, 72.0, 1748921079), (863.4437866210938, 3520.0, 72.0, 1748921080), (860.14794921875, 3515.0, 72.0, 1748921081), (856.85205078125, 3510.0, 72.0, 1748921082), (853.5562133789062, 3505.0, 72.0, 1748921083), (850.2603759765625, 3500.0, 72.0, 1748921084), (846.9644775390625, 3495.0, 72.0, 1748921085), (843.6686401367188, 3490.0, 72.0, 1748921086), (840.372802734375, 3485.0, 72.0, 1748921087), (837.076904296875, 3480.0, 72.0, 1748921088), (833.7810668945312, 3475.0, 72.0, 1748921089), (830.4852294921875, 3470.0, 72.0, 1748921090), (827.1893310546875, 3465.0, 72.0, 1748921091), (823.8934936523438, 3460.0, 72.0, 1748921092), (820.59765625, 3455.0, 72.0, 1748921093), (817.3017578125, 3450.0, 72.0, 1748921094), (814.0059204101562, 3445.0, 72.0, 1748921095), (810.7100830078125, 3440.0, 72.0, 1748921096), (807.4141845703125, 3435.0, 72.0, 1748921097), (804.1183471679688, 3430.0, 72.0, 1748921098), (800.822509765625, 3425.0, 72.0, 1748921099), (797.526611328125, 3420.0, 72.0, 1748921100), (794.2307739257812, 3415.0, 72.0, 1748921101), (790.9349365234375, 3410.0, 72.0, 1748921102), (787.6390380859375, 3405.0, 72.0, 1748921103), (784.3432006835938, 3400.0, 72.0, 1748921104), (781.04736328125, 3395.0, 72.0, 1748921105), (777.75146484375, 3390.0, 72.0, 1748921106), (774.4556274414062, 3385.0, 72.0, 1748921107), (771.1597900390625, 3380.0, 72.0, 1748921108), (767.8638916015625, 3375.0, 72.0, 1748921109), (764.5680541992188, 3370.0, 72.0, 1748921110), (761.272216796875, 3365.0, 72.0, 1748921111), (757.976318359375, 3360.0, 72.0, 1748921112), (754.6804809570312, 3355.0, 72.0, 1748921113), (751.3846435546875, 3350.0, 72.0, 1748921114), (748.0887451171875, 3345.0, 72.0, 1748921115), (744.7929077148438, 3340.0, 72.0, 1748921116), (741.4970703125, 3335.0, 72.0, 1748921117), (738.201171875, 3330.0, 72.0, 1748921118), (734.9053344726562, 3325.0, 72.0, 1748921119), (731.6094970703125, 3320.0, 72.0, 1748921120), (728.3135986328125, 3315.0, 72.0, 1748921121), (725.0177612304688, 3310.0, 72.0, 1748921122), (721.721923828125, 3305.0, 72.0, 1748921123), (718.426025390625, 3300.0, 72.0, 1748921124), (715.1301879882812, 3295.0, 72.0, 1748921125), (711.8342895507812, 3290.0, 72.0, 1748921126), (708.5384521484375, 3285.0, 72.0, 1748921127), (705.2426147460938, 3280.0, 72.0, 1748921128), (701.9467163085938, 3275.0, 72.0, 1748921129), (698.65087890625, 3270.0, 72.0, 1748921130), (695.3550415039062, 3265.0, 72.0, 1748921131), (692.0591430664062, 3260.0, 72.0, 1748921132), (688.7633056640625, 3255.0, 72.0, 1748921133), (685.4674682617188, 3250.0, 72.0, 1748921134), (682.1715698242188, 3245.0, 72.0, 1748921135), (678.875732421875, 3240.0, 72.0, 1748921136), (675.5798950195312, 3235.0, 72.0, 1748921137), (672.2839965820312, 3230.0, 72.0, 1748921138), (668.9881591796875, 3225.0, 72.0, 1748921139), (665.6923217773438, 3220.0, 72.0, 1748921140), (662.3964233398438, 3215.0, 72.0, 1748921141), (659.1005859375, 3210.0, 72.0, 1748921142), (655.8047485351562, 3205.0, 72.0, 1748921143), (652.5088500976562, 3200.0, 72.0, 1748921144), (649.2130126953125, 3195.0, 72.0, 1748921145), (645.9171752929688, 3190.0, 72.0, 1748921146), (642.6212768554688, 3185.0, 72.0, 1748921147), (639.325439453125, 3180.0, 72.0, 1748921148), (636.0296020507812, 3175.0, 72.0, 1748921149), (632.7337036132812, 3170.0, 72.0, 1748921150), (629.4378662109375, 3165.0, 72.0, 1748921151), (626.1420288085938, 3160.0, 72.0, 1748921152), (622.8461303710938, 3155.0, 72.0, 1748921153), (619.55029296875, 3150.0, 72.0, 1748921154), (616.2544555664062, 3145.0, 72.0, 1748921155), (612.9585571289062, 3140.0, 72.0, 1748921156), (609.6627197265625, 3135.0, 72.0, 1748921157), (606.3668823242188, 3130.0, 72.0, 1748921158), (603.0709838867188, 3125.0, 72.0, 1748921159), (599.775146484375, 3120.0, 72.0, 1748921160), (596.4793090820312, 3115.0, 72.0, 1748921161), (593.1834106445312, 3110.0, 72.0, 1748921162), (589.8875732421875, 3105.0, 72.0, 1748921163), (586.5917358398438, 3100.0, 72.0, 1748921164), (583.2958374023438, 3095.0, 72.0, 1748921165), (580.0, 3090.0, 72.0, 1748921166), (576.7041625976562, 3085.0, 72.0, 1748921167), (573.4082641601562, 3080.0, 72.0, 1748921168), (570.1124267578125, 3075.0, 72.0, 1748921169), (566.8165893554688, 3070.0, 72.0, 1748921170), (563.5206909179688, 3065.0, 72.0, 1748921171), (560.224853515625, 3060.0, 72.0, 1748921172), (556.9290161132812, 3055.0, 72.0, 1748921173), (553.6331176757812, 3050.0, 72.0, 1748921174), (550.3372802734375, 3045.0, 72.0, 1748921175), (547.0414428710938, 3040.0, 72.0, 1748921176), (543.7455444335938, 3035.0, 72.0, 1748921177), (540.44970703125, 3030.0, 72.0, 1748921178), (537.1538696289062, 3025.0, 72.0, 1748921179), (533.8579711914062, 3020.0, 72.0, 1748921180), (530.5621337890625, 3015.0, 72.0, 1748921181), (527.2662963867188, 3010.0, 72.0, 1748921182), (523.9703979492188, 3005.0, 72.0, 1748921183), (520.674560546875, 3000.0, 72.0, 1748921184), (517.3787231445312, 2995.0, 72.0, 1748921185), (514.0828247070312, 2990.0, 72.0, 1748921186), (510.7869873046875, 2985.0, 72.0, 1748921187), (507.4911193847656, 2980.0, 72.0, 1748921188), (504.19525146484375, 2975.0, 72.0, 1748921189), (500.8994140625, 2970.0, 72.0, 1748921190), (497.6035461425781, 2965.0, 72.0, 1748921191), (494.30767822265625, 2960.0, 72.0, 1748921192), (491.0118408203125, 2955.0, 72.0, 1748921193), (487.7159729003906, 2950.0, 72.0, 1748921194), (484.42010498046875, 2945.0, 72.0, 1748921195), (481.124267578125, 2940.0, 72.0, 1748921196), (477.8283996582031, 2935.0, 72.0, 1748921197), (474.53253173828125, 2930.0, 72.0, 1748921198), (471.2366943359375, 2925.0, 72.0, 1748921199), (467.9408264160156, 2920.0, 72.0, 1748921200), (464.64495849609375, 2915.0, 72.0, 1748921201), (461.34912109375, 2910.0, 72.0, 1748921202), (458.0532531738281, 2905.0, 72.0, 1748921203), (454.75738525390625, 2900.0, 72.0, 1748921204), (451.4615478515625, 2895.0, 72.0, 1748921205), (448.1656799316406, 2890.0, 72.0, 1748921206), (444.86981201171875, 2885.0, 72.0, 1748921207), (441.573974609375, 2880.0, 72.0, 1748921208), (438.2781066894531, 2875.0, 72.0, 1748921209), (434.98223876953125, 2870.0, 72.0, 1748921210), (431.6864013671875, 2865.0, 72.0, 1748921211), (428.3905334472656, 2860.0, 72.0, 1748921212), (425.09466552734375, 2855.0, 72.0, 1748921213), (421.798828125, 2850.0, 72.0, 1748921214), (418.5029602050781, 2845.0, 72.0, 1748921215), (415.20709228515625, 2840.0, 72.0, 1748921216), (411.9112548828125, 2835.0, 72.0, 1748921217), (408.6153869628906, 2830.0, 72.0, 1748921218), (405.31951904296875, 2825.0, 72.0, 1748921219), (402.023681640625, 2820.0, 72.0, 1748921220), (398.7278137207031, 2815.0, 72.0, 1748921221), (395.43194580078125, 2810.0, 72.0, 1748921222), (392.1361083984375, 2805.0, 72.0, 1748921223), (388.8402404785156, 2800.0, 72.0, 1748921224), (385.54437255859375, 2795.0, 72.0, 1748921225), (382.24853515625, 2790.0, 72.0, 1748921226), (378.9526672363281, 2785.0, 72.0, 1748921227), (375.65679931640625, 2780.0, 72.0, 1748921228), (372.3609619140625, 2775.0, 72.0, 1748921229), (369.0650939941406, 2770.0, 72.0, 1748921230), (365.76922607421875, 2765.0, 72.0, 1748921231), (362.4733581542969, 2760.0, 72.0, 1748921232), (359.1775207519531, 2755.0, 72.0, 1748921233), (355.88165283203125, 2750.0, 72.0, 1748921234), (352.5857849121094, 2745.0, 72.0, 1748921235), (349.2899475097656, 2740.0, 72.0, 1748921236), (345.99407958984375, 2735.0, 72.0, 1748921237), (342.6982116699219, 2730.0, 72.0, 1748921238), (339.4023742675781, 2725.0, 72.0, 1748921239), (336.10650634765625, 2720.0, 72.0, 1748921240), (332.8106384277344, 2715.0, 72.0, 1748921241), (329.5148010253906, 2710.0, 72.0, 1748921242), (326.21893310546875, 2705.0, 72.0, 1748921243), (322.9230651855469, 2700.0, 72.0, 1748921244), (319.6272277832031, 2695.0, 72.0, 1748921245), (316.33135986328125, 2690.0, 72.0, 1748921246), (313.0354919433594, 2685.0, 72.0, 1748921247), (309.7396545410156, 2680.0, 72.0, 1748921248), (306.44378662109375, 2675.0, 72.0, 1748921249), (303.1479187011719, 2670.0, 72.0, 1748921250), (299.8520812988281, 2665.0, 72.0, 1748921251), (296.55621337890625, 2660.0, 72.0, 1748921252), (293.2603454589844, 2655.0, 72.0, 1748921253), (289.9645080566406, 2650.0, 72.0, 1748921254), (286.66864013671875, 2645.0, 72.0, 1748921255), (283.3727722167969, 2640.0, 72.0, 1748921256), (280.0769348144531, 2635.0, 72.0, 1748921257), (276.78106689453125, 2630.0, 72.0, 1748921258), (273.4851989746094, 2625.0, 72.0, 1748921259), (270.1893615722656, 2620.0, 72.0, 1748921260), (266.89349365234375, 2615.0, 72.0, 1748921261), (263.5976257324219, 2610.0, 72.0, 1748921262), (260.3017883300781, 2605.0, 72.0, 1748921263), (257.00592041015625, 2600.0, 72.0, 1748921264), (253.71005249023438, 2595.0, 72.0, 1748921265), (250.41419982910156, 2590.0, 72.0, 1748921266), (247.11834716796875, 2585.0, 72.0, 1748921267), (243.82247924804688, 2580.0, 72.0, 1748921268), (240.52662658691406, 2575.0, 72.0, 1748921269), (237.23077392578125, 2570.0, 72.0, 1748921270), (233.93490600585938, 2565.0, 72.0, 1748921271), (230.63905334472656, 2560.0, 72.0, 1748921272), (227.34320068359375, 2555.0, 72.0, 1748921273), (224.04733276367188, 2550.0, 72.0, 1748921274), (220.75148010253906, 2545.0, 72.0, 1748921275), (217.45562744140625, 2540.0, 72.0, 1748921276), (214.15975952148438, 2535.0, 72.0, 1748921277), (210.86390686035156, 2530.0, 72.0, 1748921278), (207.56805419921875, 2525.0, 72.0, 1748921279), (204.27218627929688, 2520.0, 72.0, 1748921280), (200.97633361816406, 2515.0, 72.0, 1748921281), (197.68048095703125, 2510.0, 72.0, 1748921282), (194.38461303710938, 2505.0, 72.0, 1748921283), (191.08876037597656, 2500.0, 72.0, 1748921284), (187.7928924560547, 2495.0, 72.0, 1748921285), (184.49703979492188, 2490.0, 72.0, 1748921286), (181.20118713378906, 2485.0, 72.0, 1748921287), (177.9053192138672, 2480.0, 72.0, 1748921288), (174.60946655273438, 2475.0, 72.0, 1748921289), (171.31361389160156, 2470.0, 72.0, 1748921290), (168.0177459716797, 2465.0, 72.0, 1748921291), (164.72189331054688, 2460.0, 72.0, 1748921292), (161.42604064941406, 2455.0, 72.0, 1748921293), (158.1301727294922, 2450.0, 72.0, 1748921294), (154.83432006835938, 2445.0, 72.0, 1748921295), (151.53846740722656, 2440.0, 72.0, 1748921296), (148.2425994873047, 2435.0, 72.0, 1748921297), (144.94674682617188, 2430.0, 72.0, 1748921298), (141.65089416503906, 2425.0, 72.0, 1748921299), (138.3550262451172, 2420.0, 72.0, 1748921300), (135.05917358398438, 2415.0, 72.0, 1748921301), (131.76332092285156, 2410.0, 72.0, 1748921302), (128.4674530029297, 2405.0, 72.0, 1748921303), (125.17160034179688, 2400.0, 72.0, 1748921304), (121.87574005126953, 2395.0, 72.0, 1748921305), (118.57987976074219, 2390.0, 72.0, 1748921306), (115.28402709960938, 2385.0, 72.0, 1748921307), (111.98816680908203, 2380.0, 72.0, 1748921308), (108.69230651855469, 2375.0, 72.0, 1748921309), (105.39644622802734, 2370.0, 72.0, 1748921310), (102.10059356689453, 2365.0, 72.0, 1748921311), (98.80473327636719, 2360.0, 72.0, 1748921312), (95.50887298583984, 2355.0, 72.0, 1748921313), (92.21302032470703, 2350.0, 72.0, 1748921314), (88.91716003417969, 2345.0, 72.0, 1748921315), (85.62129974365234, 2340.0, 72.0, 1748921316), (82.32544708251953, 2335.0, 72.0, 1748921317), (79.02958679199219, 2330.0, 72.0, 1748921318), (75.73372650146484, 2325.0, 72.0, 1748921319), (72.4378662109375, 2320.0, 72.0, 1748921320), (69.14201354980469, 2315.0, 72.0, 1748921321), (65.84615325927734, 2310.0, 72.0, 1748921322), (62.550296783447266, 2305.0, 72.0, 1748921323), (59.25443649291992, 2300.0, 72.0, 1748921324), (55.958580017089844, 2295.0, 72.0, 1748921325), (52.662723541259766, 2290.0, 72.0, 1748921326), (49.36686325073242, 2285.0, 72.0, 1748921327), (46.071006774902344, 2280.0, 72.0, 1748921328), (42.775146484375, 2275.0, 72.0, 1748921329), (39.47929000854492, 2270.0, 72.0, 1748921330), (36.183433532714844, 2265.0, 72.0, 1748921331), (32.8875732421875, 2260.0, 72.0, 1748921332), (29.591716766357422, 2255.0, 72.0, 1748921333), (26.29585838317871, 2250.0, 72.0, 1748921334), (23.0, 2245.0, 72.0, 1748921335), (23.0, 2245.0, 67.0, 1748921360), (23.0, 2245.0, 63.0, 1748921380), (23.0, 2245.0, 58.0, 1748921405), (23.0, 2245.0, 53.0, 1748921430), (23.0, 2245.0, 49.0, 1748921450), (23.0, 2245.0, 44.0, 1748921475), (23.0, 2245.0, 39.0, 1748921500), (23.0, 2245.0, 35.0, 1748921520), (23.0, 2245.0, 30.0, 1748921545)], '1419946c-9691-42a6-9d6d-f7f37dd843c3'
2025-06-03 11:20:20,084 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 48.5119 秒
2025-06-03 11:20:20,111 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 48.5386 秒
2025-06-03 11:20:20,196 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489208010478207，共 3 个点
2025-06-03 11:20:24,244 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '05a0583f-41ce-4f79-93b5-b9d328798ab5', 'id': '1419946c-9691-42a6-9d6d-f7f37dd843c3', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748920820, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: add_path(): incompatible function arguments. The following argument types are supported:\n    1. (self: pathfinding_cpp.OccupancyMap, flight_id: str, path: list) -> None\n\nInvoked with: <pathfinding_cpp.OccupancyMap object at 0x0000025A60E601B0>, [(1137.0, 3935.0, 30.0, 1748920787), (1137.0, 3935.0, 35.0, 1748920812), (1137.0, 3935.0, 40.0, 1748920837), (1137.0, 3935.0, 45.0, 1748920862), (1137.0, 3935.0, 50.0, 1748920887), (1137.0, 3935.0, 55.0, 1748920912), (1137.0, 3935.0, 60.0, 1748920937), (1137.0, 3935.0, 65.0, 1748920962), (1137.0, 3935.0, 70.0, 1748920987), (1137.0, 3935.0, 72.0, 1748920997), (1133.7041015625, 3930.0, 72.0, 1748920998), (1130.4083251953125, 3925.0, 72.0, 1748920999), (1127.1124267578125, 3920.0, 72.0, 1748921000), (1123.8165283203125, 3915.0, 72.0, 1748921001), (1120.520751953125, 3910.0, 72.0, 1748921002), (1117.224853515625, 3905.0, 72.0, 1748921003), (1113.928955078125, 3900.0, 72.0, 1748921004), (1110.6331787109375, 3895.0, 72.0, 1748921005), (1107.3372802734375, 3890.0, 72.0, 1748921006), (1104.0413818359375, 3885.0, 72.0, 1748921007), (1100.74560546875, 3880.0, 72.0, 1748921008), (1097.44970703125, 3875.0, 72.0, 1748921009), (1094.15380859375, 3870.0, 72.0, 1748921010), (1090.8580322265625, 3865.0, 72.0, 1748921011), (1087.5621337890625, 3860.0, 72.0, 1748921012), (1084.2662353515625, 3855.0, 72.0, 1748921013), (1080.970458984375, 3850.0, 72.0, 1748921014), (1077.674560546875, 3845.0, 72.0, 1748921015), (1074.378662109375, 3840.0, 72.0, 1748921016), (1071.0828857421875, 3835.0, 72.0, 1748921017), (1067.7869873046875, 3830.0, 72.0, 1748921018), (1064.4910888671875, 3825.0, 72.0, 1748921019), (1061.1953125, 3820.0, 72.0, 1748921020), (1057.8994140625, 3815.0, 72.0, 1748921021), (1054.603515625, 3810.0, 72.0, 1748921022), (1051.3077392578125, 3805.0, 72.0, 1748921023), (1048.0118408203125, 3800.0, 72.0, 1748921024), (1044.7159423828125, 3795.0, 72.0, 1748921025), (1041.420166015625, 3790.0, 72.0, 1748921026), (1038.124267578125, 3785.0, 72.0, 1748921027), (1034.828369140625, 3780.0, 72.0, 1748921028), (1031.5325927734375, 3775.0, 72.0, 1748921029), (1028.2366943359375, 3770.0, 72.0, 1748921030), (1024.9407958984375, 3765.0, 72.0, 1748921031), (1021.6449584960938, 3760.0, 72.0, 1748921032), (1018.34912109375, 3755.0, 72.0, 1748921033), (1015.0532836914062, 3750.0, 72.0, 1748921034), (1011.7573852539062, 3745.0, 72.0, 1748921035), (1008.4615478515625, 3740.0, 72.0, 1748921036), (1005.1657104492188, 3735.0, 72.0, 1748921037), (1001.8698120117188, 3730.0, 72.0, 1748921038), (998.573974609375, 3725.0, 72.0, 1748921039), (995.278076171875, 3720.0, 72.0, 1748921040), (991.9822387695312, 3715.0, 72.0, 1748921041), (988.6864013671875, 3710.0, 72.0, 1748921042), (985.3905029296875, 3705.0, 72.0, 1748921043), (982.0946655273438, 3700.0, 72.0, 1748921044), (978.798828125, 3695.0, 72.0, 1748921045), (975.5029296875, 3690.0, 72.0, 1748921046), (972.2070922851562, 3685.0, 72.0, 1748921047), (968.9112548828125, 3680.0, 72.0, 1748921048), (965.6153564453125, 3675.0, 72.0, 1748921049), (962.3195190429688, 3670.0, 72.0, 1748921050), (959.023681640625, 3665.0, 72.0, 1748921051), (955.727783203125, 3660.0, 72.0, 1748921052), (952.4319458007812, 3655.0, 72.0, 1748921053), (949.1361083984375, 3650.0, 72.0, 1748921054), (945.8402099609375, 3645.0, 72.0, 1748921055), (942.5443725585938, 3640.0, 72.0, 1748921056), (939.24853515625, 3635.0, 72.0, 1748921057), (935.95263671875, 3630.0, 72.0, 1748921058), (932.6567993164062, 3625.0, 72.0, 1748921059), (929.3609619140625, 3620.0, 72.0, 1748921060), (926.0650634765625, 3615.0, 72.0, 1748921061), (922.7692260742188, 3610.0, 72.0, 1748921062), (919.473388671875, 3605.0, 72.0, 1748921063), (916.177490234375, 3600.0, 72.0, 1748921064), (912.8816528320312, 3595.0, 72.0, 1748921065), (909.5858154296875, 3590.0, 72.0, 1748921066), (906.2899169921875, 3585.0, 72.0, 1748921067), (902.9940795898438, 3580.0, 72.0, 1748921068), (899.6982421875, 3575.0, 72.0, 1748921069), (896.40234375, 3570.0, 72.0, 1748921070), (893.1065063476562, 3565.0, 72.0, 1748921071), (889.8106689453125, 3560.0, 72.0, 1748921072), (886.5147705078125, 3555.0, 72.0, 1748921073), (883.2189331054688, 3550.0, 72.0, 1748921074), (879.923095703125, 3545.0, 72.0, 1748921075), (876.627197265625, 3540.0, 72.0, 1748921076), (873.3313598632812, 3535.0, 72.0, 1748921077), (870.0355224609375, 3530.0, 72.0, 1748921078), (866.7396240234375, 3525.0, 72.0, 1748921079), (863.4437866210938, 3520.0, 72.0, 1748921080), (860.14794921875, 3515.0, 72.0, 1748921081), (856.85205078125, 3510.0, 72.0, 1748921082), (853.5562133789062, 3505.0, 72.0, 1748921083), (850.2603759765625, 3500.0, 72.0, 1748921084), (846.9644775390625, 3495.0, 72.0, 1748921085), (843.6686401367188, 3490.0, 72.0, 1748921086), (840.372802734375, 3485.0, 72.0, 1748921087), (837.076904296875, 3480.0, 72.0, 1748921088), (833.7810668945312, 3475.0, 72.0, 1748921089), (830.4852294921875, 3470.0, 72.0, 1748921090), (827.1893310546875, 3465.0, 72.0, 1748921091), (823.8934936523438, 3460.0, 72.0, 1748921092), (820.59765625, 3455.0, 72.0, 1748921093), (817.3017578125, 3450.0, 72.0, 1748921094), (814.0059204101562, 3445.0, 72.0, 1748921095), (810.7100830078125, 3440.0, 72.0, 1748921096), (807.4141845703125, 3435.0, 72.0, 1748921097), (804.1183471679688, 3430.0, 72.0, 1748921098), (800.822509765625, 3425.0, 72.0, 1748921099), (797.526611328125, 3420.0, 72.0, 1748921100), (794.2307739257812, 3415.0, 72.0, 1748921101), (790.9349365234375, 3410.0, 72.0, 1748921102), (787.6390380859375, 3405.0, 72.0, 1748921103), (784.3432006835938, 3400.0, 72.0, 1748921104), (781.04736328125, 3395.0, 72.0, 1748921105), (777.75146484375, 3390.0, 72.0, 1748921106), (774.4556274414062, 3385.0, 72.0, 1748921107), (771.1597900390625, 3380.0, 72.0, 1748921108), (767.8638916015625, 3375.0, 72.0, 1748921109), (764.5680541992188, 3370.0, 72.0, 1748921110), (761.272216796875, 3365.0, 72.0, 1748921111), (757.976318359375, 3360.0, 72.0, 1748921112), (754.6804809570312, 3355.0, 72.0, 1748921113), (751.3846435546875, 3350.0, 72.0, 1748921114), (748.0887451171875, 3345.0, 72.0, 1748921115), (744.7929077148438, 3340.0, 72.0, 1748921116), (741.4970703125, 3335.0, 72.0, 1748921117), (738.201171875, 3330.0, 72.0, 1748921118), (734.9053344726562, 3325.0, 72.0, 1748921119), (731.6094970703125, 3320.0, 72.0, 1748921120), (728.3135986328125, 3315.0, 72.0, 1748921121), (725.0177612304688, 3310.0, 72.0, 1748921122), (721.721923828125, 3305.0, 72.0, 1748921123), (718.426025390625, 3300.0, 72.0, 1748921124), (715.1301879882812, 3295.0, 72.0, 1748921125), (711.8342895507812, 3290.0, 72.0, 1748921126), (708.5384521484375, 3285.0, 72.0, 1748921127), (705.2426147460938, 3280.0, 72.0, 1748921128), (701.9467163085938, 3275.0, 72.0, 1748921129), (698.65087890625, 3270.0, 72.0, 1748921130), (695.3550415039062, 3265.0, 72.0, 1748921131), (692.0591430664062, 3260.0, 72.0, 1748921132), (688.7633056640625, 3255.0, 72.0, 1748921133), (685.4674682617188, 3250.0, 72.0, 1748921134), (682.1715698242188, 3245.0, 72.0, 1748921135), (678.875732421875, 3240.0, 72.0, 1748921136), (675.5798950195312, 3235.0, 72.0, 1748921137), (672.2839965820312, 3230.0, 72.0, 1748921138), (668.9881591796875, 3225.0, 72.0, 1748921139), (665.6923217773438, 3220.0, 72.0, 1748921140), (662.3964233398438, 3215.0, 72.0, 1748921141), (659.1005859375, 3210.0, 72.0, 1748921142), (655.8047485351562, 3205.0, 72.0, 1748921143), (652.5088500976562, 3200.0, 72.0, 1748921144), (649.2130126953125, 3195.0, 72.0, 1748921145), (645.9171752929688, 3190.0, 72.0, 1748921146), (642.6212768554688, 3185.0, 72.0, 1748921147), (639.325439453125, 3180.0, 72.0, 1748921148), (636.0296020507812, 3175.0, 72.0, 1748921149), (632.7337036132812, 3170.0, 72.0, 1748921150), (629.4378662109375, 3165.0, 72.0, 1748921151), (626.1420288085938, 3160.0, 72.0, 1748921152), (622.8461303710938, 3155.0, 72.0, 1748921153), (619.55029296875, 3150.0, 72.0, 1748921154), (616.2544555664062, 3145.0, 72.0, 1748921155), (612.9585571289062, 3140.0, 72.0, 1748921156), (609.6627197265625, 3135.0, 72.0, 1748921157), (606.3668823242188, 3130.0, 72.0, 1748921158), (603.0709838867188, 3125.0, 72.0, 1748921159), (599.775146484375, 3120.0, 72.0, 1748921160), (596.4793090820312, 3115.0, 72.0, 1748921161), (593.1834106445312, 3110.0, 72.0, 1748921162), (589.8875732421875, 3105.0, 72.0, 1748921163), (586.5917358398438, 3100.0, 72.0, 1748921164), (583.2958374023438, 3095.0, 72.0, 1748921165), (580.0, 3090.0, 72.0, 1748921166), (576.7041625976562, 3085.0, 72.0, 1748921167), (573.4082641601562, 3080.0, 72.0, 1748921168), (570.1124267578125, 3075.0, 72.0, 1748921169), (566.8165893554688, 3070.0, 72.0, 1748921170), (563.5206909179688, 3065.0, 72.0, 1748921171), (560.224853515625, 3060.0, 72.0, 1748921172), (556.9290161132812, 3055.0, 72.0, 1748921173), (553.6331176757812, 3050.0, 72.0, 1748921174), (550.3372802734375, 3045.0, 72.0, 1748921175), (547.0414428710938, 3040.0, 72.0, 1748921176), (543.7455444335938, 3035.0, 72.0, 1748921177), (540.44970703125, 3030.0, 72.0, 1748921178), (537.1538696289062, 3025.0, 72.0, 1748921179), (533.8579711914062, 3020.0, 72.0, 1748921180), (530.5621337890625, 3015.0, 72.0, 1748921181), (527.2662963867188, 3010.0, 72.0, 1748921182), (523.9703979492188, 3005.0, 72.0, 1748921183), (520.674560546875, 3000.0, 72.0, 1748921184), (517.3787231445312, 2995.0, 72.0, 1748921185), (514.0828247070312, 2990.0, 72.0, 1748921186), (510.7869873046875, 2985.0, 72.0, 1748921187), (507.4911193847656, 2980.0, 72.0, 1748921188), (504.19525146484375, 2975.0, 72.0, 1748921189), (500.8994140625, 2970.0, 72.0, 1748921190), (497.6035461425781, 2965.0, 72.0, 1748921191), (494.30767822265625, 2960.0, 72.0, 1748921192), (491.0118408203125, 2955.0, 72.0, 1748921193), (487.7159729003906, 2950.0, 72.0, 1748921194), (484.42010498046875, 2945.0, 72.0, 1748921195), (481.124267578125, 2940.0, 72.0, 1748921196), (477.8283996582031, 2935.0, 72.0, 1748921197), (474.53253173828125, 2930.0, 72.0, 1748921198), (471.2366943359375, 2925.0, 72.0, 1748921199), (467.9408264160156, 2920.0, 72.0, 1748921200), (464.64495849609375, 2915.0, 72.0, 1748921201), (461.34912109375, 2910.0, 72.0, 1748921202), (458.0532531738281, 2905.0, 72.0, 1748921203), (454.75738525390625, 2900.0, 72.0, 1748921204), (451.4615478515625, 2895.0, 72.0, 1748921205), (448.1656799316406, 2890.0, 72.0, 1748921206), (444.86981201171875, 2885.0, 72.0, 1748921207), (441.573974609375, 2880.0, 72.0, 1748921208), (438.2781066894531, 2875.0, 72.0, 1748921209), (434.98223876953125, 2870.0, 72.0, 1748921210), (431.6864013671875, 2865.0, 72.0, 1748921211), (428.3905334472656, 2860.0, 72.0, 1748921212), (425.09466552734375, 2855.0, 72.0, 1748921213), (421.798828125, 2850.0, 72.0, 1748921214), (418.5029602050781, 2845.0, 72.0, 1748921215), (415.20709228515625, 2840.0, 72.0, 1748921216), (411.9112548828125, 2835.0, 72.0, 1748921217), (408.6153869628906, 2830.0, 72.0, 1748921218), (405.31951904296875, 2825.0, 72.0, 1748921219), (402.023681640625, 2820.0, 72.0, 1748921220), (398.7278137207031, 2815.0, 72.0, 1748921221), (395.43194580078125, 2810.0, 72.0, 1748921222), (392.1361083984375, 2805.0, 72.0, 1748921223), (388.8402404785156, 2800.0, 72.0, 1748921224), (385.54437255859375, 2795.0, 72.0, 1748921225), (382.24853515625, 2790.0, 72.0, 1748921226), (378.9526672363281, 2785.0, 72.0, 1748921227), (375.65679931640625, 2780.0, 72.0, 1748921228), (372.3609619140625, 2775.0, 72.0, 1748921229), (369.0650939941406, 2770.0, 72.0, 1748921230), (365.76922607421875, 2765.0, 72.0, 1748921231), (362.4733581542969, 2760.0, 72.0, 1748921232), (359.1775207519531, 2755.0, 72.0, 1748921233), (355.88165283203125, 2750.0, 72.0, 1748921234), (352.5857849121094, 2745.0, 72.0, 1748921235), (349.2899475097656, 2740.0, 72.0, 1748921236), (345.99407958984375, 2735.0, 72.0, 1748921237), (342.6982116699219, 2730.0, 72.0, 1748921238), (339.4023742675781, 2725.0, 72.0, 1748921239), (336.10650634765625, 2720.0, 72.0, 1748921240), (332.8106384277344, 2715.0, 72.0, 1748921241), (329.5148010253906, 2710.0, 72.0, 1748921242), (326.21893310546875, 2705.0, 72.0, 1748921243), (322.9230651855469, 2700.0, 72.0, 1748921244), (319.6272277832031, 2695.0, 72.0, 1748921245), (316.33135986328125, 2690.0, 72.0, 1748921246), (313.0354919433594, 2685.0, 72.0, 1748921247), (309.7396545410156, 2680.0, 72.0, 1748921248), (306.44378662109375, 2675.0, 72.0, 1748921249), (303.1479187011719, 2670.0, 72.0, 1748921250), (299.8520812988281, 2665.0, 72.0, 1748921251), (296.55621337890625, 2660.0, 72.0, 1748921252), (293.2603454589844, 2655.0, 72.0, 1748921253), (289.9645080566406, 2650.0, 72.0, 1748921254), (286.66864013671875, 2645.0, 72.0, 1748921255), (283.3727722167969, 2640.0, 72.0, 1748921256), (280.0769348144531, 2635.0, 72.0, 1748921257), (276.78106689453125, 2630.0, 72.0, 1748921258), (273.4851989746094, 2625.0, 72.0, 1748921259), (270.1893615722656, 2620.0, 72.0, 1748921260), (266.89349365234375, 2615.0, 72.0, 1748921261), (263.5976257324219, 2610.0, 72.0, 1748921262), (260.3017883300781, 2605.0, 72.0, 1748921263), (257.00592041015625, 2600.0, 72.0, 1748921264), (253.71005249023438, 2595.0, 72.0, 1748921265), (250.41419982910156, 2590.0, 72.0, 1748921266), (247.11834716796875, 2585.0, 72.0, 1748921267), (243.82247924804688, 2580.0, 72.0, 1748921268), (240.52662658691406, 2575.0, 72.0, 1748921269), (237.23077392578125, 2570.0, 72.0, 1748921270), (233.93490600585938, 2565.0, 72.0, 1748921271), (230.63905334472656, 2560.0, 72.0, 1748921272), (227.34320068359375, 2555.0, 72.0, 1748921273), (224.04733276367188, 2550.0, 72.0, 1748921274), (220.75148010253906, 2545.0, 72.0, 1748921275), (217.45562744140625, 2540.0, 72.0, 1748921276), (214.15975952148438, 2535.0, 72.0, 1748921277), (210.86390686035156, 2530.0, 72.0, 1748921278), (207.56805419921875, 2525.0, 72.0, 1748921279), (204.27218627929688, 2520.0, 72.0, 1748921280), (200.97633361816406, 2515.0, 72.0, 1748921281), (197.68048095703125, 2510.0, 72.0, 1748921282), (194.38461303710938, 2505.0, 72.0, 1748921283), (191.08876037597656, 2500.0, 72.0, 1748921284), (187.7928924560547, 2495.0, 72.0, 1748921285), (184.49703979492188, 2490.0, 72.0, 1748921286), (181.20118713378906, 2485.0, 72.0, 1748921287), (177.9053192138672, 2480.0, 72.0, 1748921288), (174.60946655273438, 2475.0, 72.0, 1748921289), (171.31361389160156, 2470.0, 72.0, 1748921290), (168.0177459716797, 2465.0, 72.0, 1748921291), (164.72189331054688, 2460.0, 72.0, 1748921292), (161.42604064941406, 2455.0, 72.0, 1748921293), (158.1301727294922, 2450.0, 72.0, 1748921294), (154.83432006835938, 2445.0, 72.0, 1748921295), (151.53846740722656, 2440.0, 72.0, 1748921296), (148.2425994873047, 2435.0, 72.0, 1748921297), (144.94674682617188, 2430.0, 72.0, 1748921298), (141.65089416503906, 2425.0, 72.0, 1748921299), (138.3550262451172, 2420.0, 72.0, 1748921300), (135.05917358398438, 2415.0, 72.0, 1748921301), (131.76332092285156, 2410.0, 72.0, 1748921302), (128.4674530029297, 2405.0, 72.0, 1748921303), (125.17160034179688, 2400.0, 72.0, 1748921304), (121.87574005126953, 2395.0, 72.0, 1748921305), (118.57987976074219, 2390.0, 72.0, 1748921306), (115.28402709960938, 2385.0, 72.0, 1748921307), (111.98816680908203, 2380.0, 72.0, 1748921308), (108.69230651855469, 2375.0, 72.0, 1748921309), (105.39644622802734, 2370.0, 72.0, 1748921310), (102.10059356689453, 2365.0, 72.0, 1748921311), (98.80473327636719, 2360.0, 72.0, 1748921312), (95.50887298583984, 2355.0, 72.0, 1748921313), (92.21302032470703, 2350.0, 72.0, 1748921314), (88.91716003417969, 2345.0, 72.0, 1748921315), (85.62129974365234, 2340.0, 72.0, 1748921316), (82.32544708251953, 2335.0, 72.0, 1748921317), (79.02958679199219, 2330.0, 72.0, 1748921318), (75.73372650146484, 2325.0, 72.0, 1748921319), (72.4378662109375, 2320.0, 72.0, 1748921320), (69.14201354980469, 2315.0, 72.0, 1748921321), (65.84615325927734, 2310.0, 72.0, 1748921322), (62.550296783447266, 2305.0, 72.0, 1748921323), (59.25443649291992, 2300.0, 72.0, 1748921324), (55.958580017089844, 2295.0, 72.0, 1748921325), (52.662723541259766, 2290.0, 72.0, 1748921326), (49.36686325073242, 2285.0, 72.0, 1748921327), (46.071006774902344, 2280.0, 72.0, 1748921328), (42.775146484375, 2275.0, 72.0, 1748921329), (39.47929000854492, 2270.0, 72.0, 1748921330), (36.183433532714844, 2265.0, 72.0, 1748921331), (32.8875732421875, 2260.0, 72.0, 1748921332), (29.591716766357422, 2255.0, 72.0, 1748921333), (26.29585838317871, 2250.0, 72.0, 1748921334), (23.0, 2245.0, 72.0, 1748921335), (23.0, 2245.0, 67.0, 1748921360), (23.0, 2245.0, 63.0, 1748921380), (23.0, 2245.0, 58.0, 1748921405), (23.0, 2245.0, 53.0, 1748921430), (23.0, 2245.0, 49.0, 1748921450), (23.0, 2245.0, 44.0, 1748921475), (23.0, 2245.0, 39.0, 1748921500), (23.0, 2245.0, 35.0, 1748921520), (23.0, 2245.0, 30.0, 1748921545)], '1419946c-9691-42a6-9d6d-f7f37dd843c3'"}, 'desc': '待调整'}[0m
2025-06-03 11:20:24,274 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:20:39,380 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 11:21:39,473 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 11:22:39,136 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 11:22:43,264 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:22:43,264 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:22:43,264 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:22:43,264 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:22:43,356 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:22:43,359 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:22:43,360 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:22:43,360 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:22:43,360 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:22:43,360 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:22:43,361 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:22:43,362 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:22:43,362 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:22:43,388 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:22:43,751 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:22:43,839 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:22:50,811 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:22:50,840 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:22:50,842 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:22:50,843 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:22:50,843 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:22:51,620 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:22:51,621 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:22:51,797 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:22:51,812 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:22:51,813 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:22:51,813 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:22:51,941 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:22:51,941 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:22:52,121 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:22:52,123 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:22:52,123 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:22:52,123 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:22:52,123 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:22:52,123 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:22:52,123 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:22:52,123 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:22:52,125 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:22:52,125 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:22:52,126 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:22:52,128 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:22:52,128 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:22:52,129 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:23:07,041 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 11:23:07,041 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 11:23:07,042 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 11:23:07,042 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 11:23:07,131 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 11:23:07,133 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 11:23:07,134 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 11:23:07,134 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 11:23:07,134 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 11:23:07,135 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 11:23:07,135 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 11:23:07,135 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 11:23:07,136 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 11:23:07,170 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 11:23:07,834 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 11:23:08,161 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 11:23:14,964 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 11:23:15,032 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 11:23:15,032 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 11:23:15,033 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 11:23:15,033 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 11:23:16,712 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 11:23:16,713 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 11:23:16,841 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 11:23:16,856 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 11:23:16,857 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 11:23:16,858 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 11:23:16,999 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 11:23:16,999 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 11:23:17,171 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 11:23:17,172 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 11:23:17,173 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 11:23:17,173 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 11:23:17,173 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 11:23:17,173 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 11:23:17,174 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 11:23:17,174 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 11:23:17,174 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 11:23:17,175 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 11:23:17,175 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 11:23:17,177 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 11:23:17,177 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 11:23:17,177 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 11:24:42,505 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:24:42,505 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:24:43,583 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'fb1d69d8-ae3d-4bdc-9c2d-9db190176bb7', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748921078909, 'mode_code': 'RISK', 'data': {'id': 'fb1d69d8-ae3d-4bdc-9c2d-9db190176bb7', 'flightapplyid': 'fb1d69d8-ae3d-4bdc-9c2d-9db190176bb7', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:24:38', 'commWay': '', 'endTime': '2025-06-03 12:24:38', 'task_source': '0'}}[0m
2025-06-03 11:24:43,588 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:24:43,588 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0043 秒
2025-06-03 11:24:43,625 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'd23eec87-bb5d-46c8-9636-522808b51561', 'id': 'fb1d69d8-ae3d-4bdc-9c2d-9db190176bb7', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748921083, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 11:24:43,940 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:24:51,839 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 11:24:51,839 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 11:24:51,844 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.5秒后重试 (1/5)
2025-06-03 11:24:52,362 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.75秒后重试 (2/5)
2025-06-03 11:24:53,131 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748921151125, 'mode_code': 'RISK', 'data': {'id': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'flightapplyid': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:25:51', 'commWay': '', 'endTime': '2025-06-03 12:25:51', 'task_source': '0'}}[0m
2025-06-03 11:24:53,132 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 11:24:53,132 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 11:24:53,133 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 11:24:53,133 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 11:24:55,730 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0010 秒
2025-06-03 11:25:01,352 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 11:25:01,356 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0020 秒
2025-06-03 11:25:01,357 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 11:25:01,358 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 11:25:01,358 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 11:25:01,358 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 11:25:21,602 - src.utils.db_connection_manager - WARNING - [Thread-15808] Error checking connection status: bytearray index out of range
2025-06-03 11:25:21,605 - src.utils.db_connection_manager - ERROR - [Thread-15808] IndexError when testing connection: bytearray index out of range. Connection object might be broken.
2025-06-03 11:25:21,719 - src.utils.db_connection_manager - ERROR - [Thread-20596] IndexError when testing connection: bytearray index out of range. Connection object might be broken.
2025-06-03 11:25:27,422 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 14:18:21,753 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 14:18:21,753 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 14:18:21,753 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 14:18:21,753 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 14:18:22,840 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 14:18:22,840 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 14:18:22,840 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 14:18:22,840 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 14:18:22,840 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 14:18:22,840 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 14:18:22,840 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 14:18:22,840 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 14:18:22,840 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 14:18:22,888 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 14:18:23,337 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 14:18:23,569 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 14:18:29,895 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 14:18:29,902 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 14:18:29,902 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 14:18:29,916 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 14:18:29,916 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 14:18:30,300 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 14:18:30,300 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 14:18:30,444 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 14:18:30,462 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 14:18:30,462 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 14:18:30,462 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 14:18:30,600 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 14:18:30,600 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 14:18:30,734 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 14:18:30,734 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 14:18:30,734 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 14:18:30,734 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 14:18:30,741 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 14:18:30,741 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 14:18:30,747 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 14:18:30,747 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 14:18:30,747 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 14:18:33,160 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:18:33,160 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:18:33,203 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748921151125, 'mode_code': 'RISK', 'data': {'id': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'flightapplyid': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 11:25:51', 'commWay': '', 'endTime': '2025-06-03 12:25:51', 'task_source': '0'}}[0m
2025-06-03 14:18:33,203 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:18:33,203 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0000 秒
2025-06-03 14:18:33,268 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '61cac5f9-42fb-4c69-b3a4-53afdeaa84a7', 'id': '2877abfd-9213-44c9-bc6d-a715f212f5c9', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748931513, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 14:18:33,431 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:21:19,909 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:21:19,909 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:21:19,942 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748931739647, 'mode_code': 'RISK', 'data': {'id': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'flightapplyid': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:22:19', 'commWay': '', 'endTime': '2025-06-03 15:22:19', 'task_source': '0'}}[0m
2025-06-03 14:21:19,942 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:21:19,942 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:21:19,942 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:21:19,942 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:21:22,381 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0010 秒
2025-06-03 14:21:22,382 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:21:22,382 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0000 秒
2025-06-03 14:21:22,382 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:21:22,382 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:21:22,382 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 14:21:22,382 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:21:25,144 - src.utils.db_connection_manager - WARNING - [Thread-11432] Error checking connection status: bytearray index out of range
2025-06-03 14:21:25,149 - src.utils.db_connection_manager - ERROR - [Thread-11432] IndexError when testing connection: bytearray index out of range. Connection object might be broken.
2025-06-03 14:21:25,254 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 14:21:25,284 - src.utils.db_connection_manager - ERROR - [Thread-26660] IndexError when testing connection: bytearray index out of range. Connection object might be broken.
2025-06-03 14:21:27,732 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489316823828316，共 3 个点
2025-06-03 14:26:55,341 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 14:26:55,341 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 14:26:55,341 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 14:26:55,341 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 14:26:55,378 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 14:26:55,378 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 14:26:55,378 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 14:26:55,378 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 14:26:55,378 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 14:26:55,378 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 14:26:55,378 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 14:26:55,378 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 14:26:55,378 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 14:26:55,406 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 14:26:55,800 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 14:26:55,838 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 14:27:02,321 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 14:27:02,346 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 14:27:02,346 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 14:27:02,346 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 14:27:02,346 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 14:27:02,755 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 14:27:02,755 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 14:27:02,866 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 14:27:02,882 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 14:27:02,882 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 14:27:02,882 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 14:27:03,015 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 14:27:03,015 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 14:27:03,148 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 14:27:03,150 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 14:27:03,150 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 14:27:03,150 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 14:27:03,150 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 14:27:03,155 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 14:27:03,155 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 14:27:03,161 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 14:27:05,497 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:05,497 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:27:05,537 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748931739647, 'mode_code': 'RISK', 'data': {'id': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'flightapplyid': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:22:19', 'commWay': '', 'endTime': '2025-06-03 15:22:19', 'task_source': '0'}}[0m
2025-06-03 14:27:05,538 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:27:05,538 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0010 秒
2025-06-03 14:27:05,560 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'e9b322fb-0e65-4c8f-95d5-7855bf0a6a1d', 'id': 'ff8cb8f7-48db-4d77-b374-09a394c88c8b', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932025, 'data': {'risk_state': False, 'risk_reason': '起飞时间早于当前时间'}, 'desc': '待调整'}[0m
2025-06-03 14:27:05,711 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:33,977 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:33,977 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:27:33,977 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.5秒后重试 (1/5)
2025-06-03 14:27:34,498 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.75秒后重试 (2/5)
2025-06-03 14:27:35,255 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '067af2e9-39e1-4d84-8b60-8952434e08e5', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932113898, 'mode_code': 'RISK', 'data': {'id': '067af2e9-39e1-4d84-8b60-8952434e08e5', 'flightapplyid': '067af2e9-39e1-4d84-8b60-8952434e08e5', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:28:33', 'commWay': '', 'endTime': '2025-06-03 15:28:33', 'task_source': '0'}}[0m
2025-06-03 14:27:35,255 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:27:35,255 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:27:35,255 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:27:35,255 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:27:37,683 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0000 秒
2025-06-03 14:27:37,688 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:27:37,709 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0020 秒
2025-06-03 14:27:37,711 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:27:37,721 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:27:37,722 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 14:27:37,735 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:27:40,545 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489320577071161，共 3 个点
2025-06-03 14:27:48,292 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 8.8148 秒
2025-06-03 14:27:48,292 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 13.0364 秒
2025-06-03 14:27:48,292 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 13.0364 秒
2025-06-03 14:27:48,292 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'f3f3b884-2414-4973-b5cb-c8dfaf9e53dc', 'id': '067af2e9-39e1-4d84-8b60-8952434e08e5', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932068, 'data': {'uavrootId': '17489320577071161', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 14:41:11', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:27:48,468 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.156秒
2025-06-03 14:27:48,468 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:48,469 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:27:48,469 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 审批通过
2025-06-03 14:27:48,476 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.008秒，提交耗时 0.007秒
2025-06-03 14:27:48,735 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:48,735 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:27:48,735 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 审批通过
2025-06-03 14:27:49,011 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:49,011 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:27:49,011 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 审批通过
2025-06-03 14:27:49,175 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:49,175 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:27:49,175 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 审批通过
2025-06-03 14:27:49,435 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.008秒
2025-06-03 14:27:49,435 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:27:49,435 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:27:49,435 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 审批通过
2025-06-03 14:27:49,436 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:27:49,436 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 067af2e9-39e1-4d84-8b60-8952434e08e5 的状态为 待执行
2025-06-03 14:27:49,442 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.007秒，提交耗时 0.005秒
2025-06-03 14:27:55,402 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'tuple' object has no attribute 't'
2025-06-03 14:30:18,095 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 14:30:18,095 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 14:30:18,095 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 14:30:18,095 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 14:30:18,158 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 14:30:18,158 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 14:30:18,158 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 14:30:18,158 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 14:30:18,158 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 14:30:18,164 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 14:30:18,165 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 14:30:18,165 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 14:30:18,165 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 14:30:18,174 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 14:30:18,533 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 14:30:18,568 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 14:30:24,953 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 14:30:24,963 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 14:30:24,963 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 14:30:24,964 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 14:30:24,965 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 14:30:25,346 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 14:30:25,347 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 14:30:25,472 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 14:30:25,480 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 14:30:25,480 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 14:30:25,480 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 14:30:25,596 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 14:30:25,596 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 14:30:25,732 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 14:30:25,733 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 14:30:25,733 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 14:30:25,733 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 14:30:25,733 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 14:30:25,744 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 14:30:25,745 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 14:30:25,745 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 14:30:38,510 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:30:38,510 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:30:38,550 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '9dcd9c7f-647b-424e-9dd3-6006c66604c6', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932298279, 'mode_code': 'RISK', 'data': {'id': '9dcd9c7f-647b-424e-9dd3-6006c66604c6', 'flightapplyid': '9dcd9c7f-647b-424e-9dd3-6006c66604c6', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:31:38', 'commWay': '', 'endTime': '2025-06-03 15:31:38', 'task_source': '0'}}[0m
2025-06-03 14:30:38,557 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:30:38,557 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:30:38,558 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:30:38,558 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:30:42,701 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0000 秒
2025-06-03 14:30:42,702 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:30:42,704 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0010 秒
2025-06-03 14:30:42,705 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:30:42,706 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:30:42,706 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 14:30:42,706 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:30:44,858 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0250 秒
2025-06-03 14:30:44,858 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 6.3076 秒
2025-06-03 14:30:44,858 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 6.3076 秒
2025-06-03 14:30:44,944 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '0b1b0ed9-5528-4a05-8756-218d7f43f1b4', 'id': '9dcd9c7f-647b-424e-9dd3-6006c66604c6', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932244, 'data': {'uavrootId': '17489322427031900', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 14:44:16', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:30:45,107 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489322427031900，共 3 个点
2025-06-03 14:30:45,216 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.000秒
2025-06-03 14:30:45,216 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:30:45,216 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:30:45,216 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 9dcd9c7f-647b-424e-9dd3-6006c66604c6 的状态为 审批通过
2025-06-03 14:30:45,216 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.000秒，提交耗时 0.000秒
2025-06-03 14:30:45,365 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:30:45,365 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:30:45,365 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 9dcd9c7f-647b-424e-9dd3-6006c66604c6 的状态为 审批通过
2025-06-03 14:30:45,532 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:30:45,532 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:30:45,532 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 9dcd9c7f-647b-424e-9dd3-6006c66604c6 的状态为 审批通过
2025-06-03 14:30:45,814 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.006秒
2025-06-03 14:30:45,814 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:30:45,814 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:30:45,815 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 9dcd9c7f-647b-424e-9dd3-6006c66604c6 的状态为 审批通过
2025-06-03 14:30:45,815 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:30:45,815 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 9dcd9c7f-647b-424e-9dd3-6006c66604c6 的状态为 待执行
2025-06-03 14:30:45,819 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.005秒，提交耗时 0.003秒
2025-06-03 14:35:17,124 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 14:35:17,124 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 14:35:17,124 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 14:35:17,124 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 14:35:17,188 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 14:35:17,188 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 14:35:17,188 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 14:35:17,188 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 14:35:17,188 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 14:35:17,188 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 14:35:17,188 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 14:35:17,204 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 14:35:17,204 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 14:35:17,222 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 14:35:17,552 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 14:35:17,881 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 14:35:24,257 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 14:35:24,257 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 14:35:24,257 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 14:35:24,272 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 14:35:24,272 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 14:35:24,701 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 14:35:24,701 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 14:35:24,836 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 14:35:24,836 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 14:35:24,836 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 14:35:24,836 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 14:35:24,975 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 14:35:24,975 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 14:35:25,091 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 14:35:25,099 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 14:35:25,099 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 14:35:25,099 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 14:35:25,099 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 14:35:25,105 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 14:35:25,105 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 14:35:25,106 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 14:36:09,685 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 5 条消息，轮询耗时: 0.000秒
2025-06-03 14:36:09,685 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:09,685 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:36:09,732 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '3809743c-5db6-4ddb-9b4c-92372d1ade1d', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932608834, 'mode_code': 'RISK', 'data': {'id': '3809743c-5db6-4ddb-9b4c-92372d1ade1d', 'flightapplyid': '3809743c-5db6-4ddb-9b4c-92372d1ade1d', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': '1581F6Q8D248A00MN001', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:36:48', 'commWay': '', 'endTime': '2025-06-03 15:36:48', 'task_source': '0'}}[0m
2025-06-03 14:36:09,736 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:36:09,737 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:36:09,737 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:36:09,737 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:36:09,737 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0000 秒
2025-06-03 14:36:09,737 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:36:09,741 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0037 秒
2025-06-03 14:36:09,742 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:36:09,742 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:36:09,743 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：357, 拐点总数量：4
2025-06-03 14:36:09,744 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:36:09,770 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0263 秒
2025-06-03 14:36:09,770 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.0369 秒
2025-06-03 14:36:09,770 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0369 秒
2025-06-03 14:36:09,844 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '21e34183-e360-406b-b48c-40f799b649ec', 'id': '3809743c-5db6-4ddb-9b4c-92372d1ade1d', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932569, 'data': {'uavrootId': '17489325697379932', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 14:49:26', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:36:09,844 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:36:09,844 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': 'fde645fa-d4b5-48e6-9bf7-686d1c039b86', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932608834, 'mode_code': 'RISK', 'data': {'id': 'fde645fa-d4b5-48e6-9bf7-686d1c039b86', 'flightapplyid': 'fde645fa-d4b5-48e6-9bf7-686d1c039b86', 'flyHeight': 455.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.64720991658203,31.978568183305665,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.74376944111816,32.134694205644536,180.0', 'device_sn': '1581F6Q8D248A00MN002', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:36:48', 'commWay': '', 'endTime': '2025-06-03 15:36:48', 'task_source': '0'}}[0m
2025-06-03 14:36:09,844 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:36:09,844 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:36:09,844 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:36:09,844 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:36:09,854 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0101 秒
2025-06-03 14:36:09,854 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:36:09,856 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0013 秒
2025-06-03 14:36:09,857 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:36:09,858 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:36:09,858 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：751, 拐点总数量：4
2025-06-03 14:36:09,858 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:36:09,950 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0915 秒
2025-06-03 14:36:09,950 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.1064 秒
2025-06-03 14:36:09,950 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.1064 秒
2025-06-03 14:36:09,950 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '6436a66b-f093-4e27-81de-5424a71e32c2', 'id': 'fde645fa-d4b5-48e6-9bf7-686d1c039b86', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932569, 'data': {'uavrootId': '17489325698555050', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 14:58:06', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:36:09,966 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:36:09,969 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '6b1c97ef-2e95-441e-99b0-b768a782aa65', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932608834, 'mode_code': 'RISK', 'data': {'id': '6b1c97ef-2e95-441e-99b0-b768a782aa65', 'flightapplyid': '6b1c97ef-2e95-441e-99b0-b768a782aa65', 'flyHeight': 385.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.62974337147705,31.97371874940674,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.63806894825927,32.10658465516846,180.0', 'device_sn': '1581F6Q8D248A00MN003', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:36:48', 'commWay': '', 'endTime': '2025-06-03 15:36:48', 'task_source': '0'}}[0m
2025-06-03 14:36:09,970 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:36:09,970 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:36:09,971 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0012 秒
2025-06-03 14:36:09,971 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:36:09,973 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0020 秒
2025-06-03 14:36:09,977 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0038 秒
2025-06-03 14:36:09,977 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0000 秒
2025-06-03 14:36:09,978 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:36:09,978 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:36:09,978 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：647, 拐点总数量：146
2025-06-03 14:36:09,978 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:36:10,052 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0745 秒
2025-06-03 14:36:10,052 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.0826 秒
2025-06-03 14:36:10,068 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0985 秒
2025-06-03 14:36:10,068 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': 'af75686c-1a69-466f-8e74-612b7d0d7338', 'id': '6b1c97ef-2e95-441e-99b0-b768a782aa65', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932570, 'data': {'uavrootId': '17489325699779600', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 15:34:42', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:36:10,068 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:36:10,080 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '822c4547-11c6-4c0c-a689-c2f023454231', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932608834, 'mode_code': 'RISK', 'data': {'id': '822c4547-11c6-4c0c-a689-c2f023454231', 'flightapplyid': '822c4547-11c6-4c0c-a689-c2f023454231', 'flyHeight': 545.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.71741941975586,32.022856818559575,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.74424150990478,32.03800593507569,180.0', 'device_sn': '1581F6Q8D248A00MN004', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:36:48', 'commWay': '', 'endTime': '2025-06-03 15:36:48', 'task_source': '0'}}[0m
2025-06-03 14:36:10,081 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:36:10,082 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:36:10,082 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:36:10,082 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 14:36:10,082 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0000 秒
2025-06-03 14:36:10,083 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 14:36:10,083 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0000 秒
2025-06-03 14:36:10,083 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 14:36:10,083 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 14:36:10,083 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：156, 拐点总数量：4
2025-06-03 14:36:10,083 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 14:36:10,103 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0197 秒
2025-06-03 14:36:10,103 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.0224 秒
2025-06-03 14:36:10,103 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0224 秒
2025-06-03 14:36:10,109 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '6f191e94-77c1-4ff2-b0bd-49af2995d4e8', 'id': '822c4547-11c6-4c0c-a689-c2f023454231', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932570, 'data': {'uavrootId': '17489325700832524', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 14:51:03', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 14:36:10,109 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 14:36:10,110 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489325697379932，共 3 个点
2025-06-03 14:36:10,116 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '4879e52e-16e3-4346-8e55-260bf12bf0ce', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748932608834, 'mode_code': 'RISK', 'data': {'id': '4879e52e-16e3-4346-8e55-260bf12bf0ce', 'flightapplyid': '4879e52e-16e3-4346-8e55-260bf12bf0ce', 'flyHeight': 550.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.6824863295459,32.05143843782227,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.66952589558593,31.972002135637208,180.0', 'device_sn': '1581F6Q8D248A00MN005', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 14:36:48', 'commWay': '', 'endTime': '2025-06-03 15:36:48', 'task_source': '0'}}[0m
2025-06-03 14:36:10,116 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 14:36:10,116 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 14:36:10,117 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 14:36:10,117 - src.handlers.message_handlers.planning_handler - ERROR - 降落点位于禁飞区 '测试' 内部 - 申请ID: 4879e52e-16e3-4346-8e55-260bf12bf0ce
2025-06-03 14:36:10,117 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0012 秒
2025-06-03 14:36:10,123 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '54082151-1e06-4d1c-b417-710482aa026a', 'id': '4879e52e-16e3-4346-8e55-260bf12bf0ce', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748932570, 'data': {'risk_state': False, 'risk_reason': "降落点位于禁飞区 '测试' 内部"}, 'desc': '待调整'}[0m
2025-06-03 14:36:10,136 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 5 条，成功 5 条，失败 0 条，处理耗时 0.451秒，提交耗时 0.013秒
2025-06-03 14:36:10,137 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 7 条消息，轮询耗时: 0.001秒
2025-06-03 14:36:10,144 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:10,144 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,145 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 审批通过
2025-06-03 14:36:10,145 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,145 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 审批通过
2025-06-03 14:36:10,149 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 7 条，成功 7 条，失败 0 条，处理耗时 0.005秒，提交耗时 0.004秒
2025-06-03 14:36:10,314 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489325698555050，共 3 个点
2025-06-03 14:36:10,567 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:10,583 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,583 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 审批通过
2025-06-03 14:36:10,604 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.022秒
2025-06-03 14:36:10,604 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:10,604 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,604 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 审批通过
2025-06-03 14:36:10,604 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:36:10,604 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 待执行
2025-06-03 14:36:10,612 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.008秒，提交耗时 0.008秒
2025-06-03 14:36:10,641 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489325699779600，共 145 个点
2025-06-03 14:36:10,794 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489325700832524，共 3 个点
2025-06-03 14:36:10,877 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:10,877 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,877 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 fde645fa-d4b5-48e6-9bf7-686d1c039b86 的状态为 审批通过
2025-06-03 14:36:10,894 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.017秒
2025-06-03 14:36:10,894 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:10,894 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,895 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 6b1c97ef-2e95-441e-99b0-b768a782aa65 的状态为 审批通过
2025-06-03 14:36:10,895 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:10,896 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 fde645fa-d4b5-48e6-9bf7-686d1c039b86 的状态为 审批通过
2025-06-03 14:36:10,900 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.006秒，提交耗时 0.004秒
2025-06-03 14:36:11,008 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.109秒
2025-06-03 14:36:11,010 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,010 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,010 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 6b1c97ef-2e95-441e-99b0-b768a782aa65 的状态为 审批通过
2025-06-03 14:36:11,010 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,010 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 822c4547-11c6-4c0c-a689-c2f023454231 的状态为 审批通过
2025-06-03 14:36:11,010 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.000秒，提交耗时 0.000秒
2025-06-03 14:36:11,276 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,276 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,276 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 822c4547-11c6-4c0c-a689-c2f023454231 的状态为 审批通过
2025-06-03 14:36:11,543 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,543 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,543 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 fde645fa-d4b5-48e6-9bf7-686d1c039b86 的状态为 审批通过
2025-06-03 14:36:11,586 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.044秒
2025-06-03 14:36:11,594 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,594 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,594 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 6b1c97ef-2e95-441e-99b0-b768a782aa65 的状态为 审批通过
2025-06-03 14:36:11,595 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 14:36:11,595 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 822c4547-11c6-4c0c-a689-c2f023454231 的状态为 审批通过
2025-06-03 14:36:11,601 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.007秒，提交耗时 0.006秒
2025-06-03 14:36:11,749 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,749 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:36:11,749 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 fde645fa-d4b5-48e6-9bf7-686d1c039b86 的状态为 待执行
2025-06-03 14:36:11,911 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:11,911 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:36:11,911 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 6b1c97ef-2e95-441e-99b0-b768a782aa65 的状态为 待执行
2025-06-03 14:36:12,190 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:12,190 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 14:36:12,190 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 822c4547-11c6-4c0c-a689-c2f023454231 的状态为 待执行
2025-06-03 14:36:53,223 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.000秒
2025-06-03 14:36:53,223 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:53,223 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 14:36:53,223 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 3809743c-5db6-4ddb-9b4c-92372d1ade1d 的状态为 执行中
2025-06-03 14:36:53,223 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 14:36:53,223 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 fde645fa-d4b5-48e6-9bf7-686d1c039b86 的状态为 执行中
2025-06-03 14:36:53,230 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.007秒，提交耗时 0.007秒
2025-06-03 14:36:54,420 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:54,420 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 14:36:54,420 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 6b1c97ef-2e95-441e-99b0-b768a782aa65 的状态为 执行中
2025-06-03 14:36:55,550 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:36:55,550 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 14:36:55,550 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 822c4547-11c6-4c0c-a689-c2f023454231 的状态为 执行中
2025-06-03 14:38:38,123 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:38:38,123 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 结束-执行异常[0m
2025-06-03 14:41:42,737 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 14:41:42,737 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 结束-执行异常[0m
2025-06-03 14:46:17,340 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 14:47:17,326 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 14:48:17,333 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 14:49:17,328 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 14:50:17,398 - src.handlers.risk_assessment_consumer - INFO - 航班 1581F6Q8D248A00MN001 的降落时间 2025-06-03 14:49:26 已过，将清理路径
2025-06-03 15:16:47,884 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 15:16:47,885 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 15:16:47,885 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 15:16:47,886 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 15:16:47,940 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 15:16:47,942 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 15:16:47,942 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 15:16:47,943 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 15:16:47,943 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 15:16:47,943 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 15:16:47,943 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 15:16:47,943 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 15:16:47,944 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 15:16:47,949 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 15:16:48,302 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 15:16:48,335 - src.handlers.message_handlers.base - INFO - 从数据库中找到 2 个禁飞区
2025-06-03 15:16:56,060 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 15:17:01,295 - src.handlers.message_handlers.base - INFO - 成功加载圆形禁飞区: 测试圆形, 中心点: (32.036260893728034, 118.57020834151761), 半径: 2192.6222米
2025-06-03 15:17:01,316 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 15:17:01,317 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 15:17:01,318 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 15:17:01,318 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 15:17:01,714 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 15:17:01,714 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 15:17:01,842 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 15:17:01,851 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 15:17:01,852 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 15:17:01,852 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 15:17:01,976 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 15:17:01,977 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 15:17:02,101 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 15:17:02,103 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 15:17:02,105 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 15:17:02,105 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 15:17:02,105 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 15:17:02,105 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 15:17:02,106 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 15:17:02,106 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 15:17:02,111 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 15:17:02,113 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 15:17:02,113 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 15:17:02,116 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 15:17:02,116 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 15:17:02,117 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 15:17:04,305 - src.handlers.message_handlers.no_fly_zone_handler - INFO - 收到禁飞区消息: {'no_fly_zone_name': '测试圆形', 'center': {'lon': 118.57020834151761, 'lat': 32.036260893728034}, 'startTime': '1748891628000', 'endTime': '1748978030000', 'state': True, 'radius': 2192.6222}
2025-06-03 15:17:04,306 - src.handlers.message_handlers.no_fly_zone_handler - ERROR - 处理禁飞区消息时出错: add_solid_cylindrical_no_fly_zone(): incompatible function arguments. The following argument types are supported:
    1. (self: pathfinding_cpp.Map3D, center_lat_deg: float, center_lon_deg: float, radius_meters: float, zone_name: str, buffer_distance_meters: int = 0, planned_paths_dict: dict = {}, boundary_thickness_grids: int = 1) -> tuple

Invoked with: <pathfinding_cpp.Map3D object at 0x0000018B9CD27130>, 32.036260893728034, 118.57020834151761, 2192.6222, '测试圆形', {}
2025-06-03 15:17:28,943 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:28,943 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 15:17:28,979 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.5秒后重试 (1/5)
2025-06-03 15:17:29,489 - src.handlers.risk_assessment_consumer - WARNING - Redis数据未就绪，等待0.75秒后重试 (2/5)
2025-06-03 15:17:30,244 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '0bf45c0f-f709-4839-8706-fc64cff8c93e', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748935108901, 'mode_code': 'RISK', 'data': {'id': '0bf45c0f-f709-4839-8706-fc64cff8c93e', 'flightapplyid': '0bf45c0f-f709-4839-8706-fc64cff8c93e', 'flyHeight': 530.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57451132344238,32.03964034211914,450', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.57451132344238,31.968611823442384,450', 'device_sn': '1581F6Q8D248A00MN001', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 15:18:28', 'commWay': '', 'endTime': '2025-06-03 16:18:28', 'task_source': '0'}}[0m
2025-06-03 15:17:30,250 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 15:17:30,250 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 15:17:30,251 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 15:17:30,251 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 15:17:30,253 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0020 秒
2025-06-03 15:17:30,253 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 15:17:30,254 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0010 秒
2025-06-03 15:17:30,255 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 15:17:30,256 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 15:17:30,257 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：342, 拐点总数量：61
2025-06-03 15:17:30,257 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 15:17:30,287 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0305 秒
2025-06-03 15:17:30,289 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.0453 秒
2025-06-03 15:17:30,289 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0453 秒
2025-06-03 15:17:30,307 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '9e81f43b-68cd-47a9-8b60-c0745955d922', 'id': '0bf45c0f-f709-4839-8706-fc64cff8c93e', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748935050, 'data': {'uavrootId': '17489350502536175', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 15:49:53', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 15:17:30,316 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.004秒
2025-06-03 15:17:30,317 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:30,317 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 15:17:30,370 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '7c07c22f-903d-4125-bc5e-f06e95c96e82', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748935108902, 'mode_code': 'RISK', 'data': {'id': '7c07c22f-903d-4125-bc5e-f06e95c96e82', 'flightapplyid': '7c07c22f-903d-4125-bc5e-f06e95c96e82', 'flyHeight': 530.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57472590016357,32.03964034211914,450', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.57472590016357,31.968611823442384,450', 'device_sn': '1581F6Q8D248A00MN002', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 15:18:28', 'commWay': '', 'endTime': '2025-06-03 16:18:28', 'task_source': '0'}}[0m
2025-06-03 15:17:30,371 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 15:17:30,371 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 15:17:30,372 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标转换耗时: 0.0000 秒
2025-06-03 15:17:30,372 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 禁飞区检查耗时: 0.0000 秒
2025-06-03 15:17:30,374 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 路径规划耗时: 0.0025 秒
2025-06-03 15:17:30,374 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 坐标反向转换耗时: 0.0000 秒
2025-06-03 15:17:30,376 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 数据库操作提交耗时: 0.0010 秒
2025-06-03 15:17:30,376 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: uav_topic
2025-06-03 15:17:30,377 - src.handlers.message_handlers.base - INFO - MQTT消息发送成功: idspace/wayline/sim
2025-06-03 15:17:30,377 - src.handlers.message_handlers.planning_handler - INFO - 路径规划成功, 路径点总数量：341, 拐点总数量：44
2025-06-03 15:17:30,377 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 存储路径耗时: 0.0000 秒
2025-06-03 15:17:30,415 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 占用图更新耗时: 0.0381 秒
2025-06-03 15:17:30,416 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间: 0.0456 秒
2025-06-03 15:17:30,417 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0466 秒
2025-06-03 15:17:30,422 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '35c130a6-b467-4b99-ac81-dcb64827036c', 'id': '7c07c22f-903d-4125-bc5e-f06e95c96e82', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748935050, 'data': {'uavrootId': '17489350503757978', 'uavroot': '青岛_自动规划路径 ', 'landing_time': '2025-06-03 15:48:17', 'adjustment_count': 0, 'risk_state': True, 'risk_reason': '航线自动路径规划成功'}, 'desc': '流量评估'}[0m
2025-06-03 15:17:30,426 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.109秒，提交耗时 0.004秒
2025-06-03 15:17:30,510 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489350502536175，共 60 个点
2025-06-03 15:17:30,685 - src.handlers.message_handlers.base - INFO - 成功保存路径数据到自动路径规划数据库，route_id: 17489350503757978，共 43 个点
2025-06-03 15:17:30,701 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.001秒
2025-06-03 15:17:30,702 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:30,702 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:30,702 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 审批通过
2025-06-03 15:17:30,708 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.006秒，提交耗时 0.006秒
2025-06-03 15:17:30,862 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.153秒
2025-06-03 15:17:30,862 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:30,862 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:30,863 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 审批通过
2025-06-03 15:17:30,863 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:30,863 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 审批通过
2025-06-03 15:17:30,867 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.004秒，提交耗时 0.003秒
2025-06-03 15:17:31,393 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:31,394 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:31,394 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 审批通过
2025-06-03 15:17:31,424 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.025秒
2025-06-03 15:17:31,424 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:31,425 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:31,425 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 审批通过
2025-06-03 15:17:31,425 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 15:17:31,425 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 待执行
2025-06-03 15:17:31,431 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.007秒，提交耗时 0.006秒
2025-06-03 15:17:31,588 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:31,588 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:31,588 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 审批通过
2025-06-03 15:17:31,860 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:31,861 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:31,861 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 审批通过
2025-06-03 15:17:32,023 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:32,023 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:32,024 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 审批通过
2025-06-03 15:17:32,296 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者一次获取到 2 条消息，轮询耗时: 0.006秒
2025-06-03 15:17:32,297 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:17:32,297 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 流量评估 -> 审批通过[0m
2025-06-03 15:17:32,297 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 审批通过
2025-06-03 15:17:32,297 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 审批通过 -> 待执行[0m
2025-06-03 15:17:32,298 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 待执行
2025-06-03 15:17:32,303 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者批量处理完成: 总消息 2 条，成功 2 条，失败 0 条，处理耗时 0.007秒，提交耗时 0.004秒
2025-06-03 15:18:31,279 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:18:31,280 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 15:18:31,280 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 0bf45c0f-f709-4839-8706-fc64cff8c93e 的状态为 执行中
2025-06-03 15:18:32,341 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 15:18:32,341 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待执行 -> 执行中[0m
2025-06-03 15:18:32,341 - src.handlers.message_handlers.state_handler - INFO - 已更新内存中planned_paths中航班 7c07c22f-903d-4125-bc5e-f06e95c96e82 的状态为 执行中
2025-06-03 15:27:48,057 - src.handlers.risk_assessment_consumer - INFO - 定期清理已完成，没有发现需要清理的航班，还剩余 2 个航班路径。存在禁飞区：['测试', '测试圆形']
