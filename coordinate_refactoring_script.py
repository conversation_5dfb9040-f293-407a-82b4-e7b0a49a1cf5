#!/usr/bin/env python3
"""
坐标系统重构脚本
用于批量替换JPS.cpp中剩余的x, y, z变量名为更直观的lon_idx, lat_idx, alt_idx
"""

import re
import os

def refactor_jps_coordinates():
    """重构JPS.cpp中的坐标变量名"""
    
    file_path = "cpp_implementation/src/JPS.cpp"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换规则
    replacements = [
        # _jump函数中的变量
        (r'\bcurr_x\b', 'curr_lon_idx'),
        (r'\bcurr_y\b', 'curr_lat_idx'),
        (r'\bcurr_z\b', 'curr_alt_idx'),
        (r'\bparent_x\b', 'parent_lon_idx'),
        (r'\bparent_y\b', 'parent_lat_idx'),
        (r'\bparent_z\b', 'parent_alt_idx'),
        (r'\bnext_x\b', 'next_lon_idx'),
        (r'\bnext_y\b', 'next_lat_idx'),
        (r'\bnext_z\b', 'next_alt_idx'),
        (r'\bgoal_x\b', 'goal_lon_idx'),
        (r'\bgoal_y\b', 'goal_lat_idx'),
        (r'\bgoal_z\b', 'goal_alt_idx'),
        
        # has_line_of_sight函数中的变量
        (r'\bstart_x\b', 'start_lon_idx'),
        (r'\bstart_y\b', 'start_lat_idx'),
        (r'\bstart_z\b', 'start_alt_idx'),
        (r'\bend_x\b', 'end_lon_idx'),
        (r'\bend_y\b', 'end_lat_idx'),
        (r'\bend_z\b', 'end_alt_idx'),
        (r'\blast_x\b', 'last_lon_idx'),
        (r'\blast_y\b', 'last_lat_idx'),
        
        # 其他函数中的变量
        (r'\bintermediate_x\b', 'intermediate_lon_idx'),
        (r'\bintermediate_y\b', 'intermediate_lat_idx'),
        (r'\bintermediate_z\b', 'intermediate_alt_idx'),
        (r'\bvertical_z\b', 'vertical_alt_idx'),
        (r'\bhorizontal_x\b', 'horizontal_lon_idx'),
        (r'\bhorizontal_y\b', 'horizontal_lat_idx'),
        
        # 通用的x, y, z变量（需要小心，避免误替换）
        (r'\bconst int x\b', 'const int lon_idx'),
        (r'\bconst int y\b', 'const int lat_idx'),
        (r'\bint x\b', 'int lon_idx'),
        (r'\bint y\b', 'int lat_idx'),
        
        # 注释中的坐标说明
        (r'x方向', '经度方向'),
        (r'y方向', '纬度方向'),
        (r'z方向', '高度方向'),
        (r'X方向', '经度方向'),
        (r'Y方向', '纬度方向'),
        (r'Z方向', '高度方向'),
        
        # 错误信息中的坐标格式
        (r'x=', 'lon='),
        (r'y=', 'lat='),
        (r'z=', 'alt='),
    ]
    
    # 应用替换
    modified_content = content
    changes_made = 0
    
    for pattern, replacement in replacements:
        new_content = re.sub(pattern, replacement, modified_content)
        if new_content != modified_content:
            changes_made += 1
            print(f"替换 '{pattern}' -> '{replacement}'")
            modified_content = new_content
    
    # 如果有修改，写回文件
    if changes_made > 0:
        # 备份原文件
        backup_path = file_path + ".backup"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"原文件已备份到: {backup_path}")
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"完成! 共进行了 {changes_made} 项替换")
    else:
        print("没有找到需要替换的内容")

def print_remaining_issues():
    """检查并打印剩余的坐标命名问题"""
    
    file_path = "cpp_implementation/src/JPS.cpp"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找可能需要更新的模式
    patterns_to_check = [
        r'\b[a-zA-Z_]*[xX]\b',  # 包含x的变量
        r'\b[a-zA-Z_]*[yY]\b',  # 包含y的变量
        r'\b[a-zA-Z_]*[zZ]\b',  # 包含z的变量
    ]
    
    issues = []
    for line_num, line in enumerate(lines, 1):
        for pattern in patterns_to_check:
            matches = re.findall(pattern, line)
            for match in matches:
                # 排除一些不需要修改的情况
                if match.lower() not in ['xyz', 'max', 'size', 'lazy', 'crazy', 'analyze']:
                    issues.append((line_num, match, line.strip()))
    
    if issues:
        print("\n可能需要检查的坐标相关变量:")
        for line_num, match, line in issues[:20]:  # 只显示前20个
            print(f"行 {line_num}: {match} -> {line}")
    else:
        print("没有发现明显的坐标命名问题")

if __name__ == "__main__":
    print("开始坐标系统重构...")
    refactor_jps_coordinates()
    print("\n检查剩余问题...")
    print_remaining_issues()
