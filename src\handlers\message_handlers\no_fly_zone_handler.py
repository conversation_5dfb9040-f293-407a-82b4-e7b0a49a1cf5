import re
from typing import Dict, List, Optional, Tuple
import time
from datetime import datetime
from ...utils.logging import get_logger
from ...utils.exceptions import MapError
from .base import MessageHandler
from ...config import settings

logger = get_logger(__name__)


class NoFlyZoneMessageHandler(MessageHandler):
    """处理禁飞区消息"""

    def handle_message(self, message: Dict) -> Tuple[Optional[List], Optional[str]]:
        """
        处理禁飞区添加或删除请求

        Args:
            message: 消息字典，包含禁飞区信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (空列表, 成功/错误信息)
        """
        try:
            zone_name = message.get("no_fly_zone_name", "zone_" + str(int(time.time())))
            state = message.get("state")

            logger.info(f"收到禁飞区消息: {message}")

            # 如果state为空或False，则删除禁飞区
            if "state" in message and (not state or state is False):
                # 删除禁飞区
                success = self.map.remove_no_fly_zone(zone_name)
                if success:
                    logger.info(f"删除禁飞区成功: {zone_name}")
                    return [], None
                else:
                    error_msg = f"删除禁飞区失败: 禁飞区 {zone_name} 不存在"
                    logger.error(error_msg)
                    return None, error_msg

            # 添加禁飞区的逻辑
            if "center" in message and "radius" in message:
                # 处理圆柱体禁飞区
                center = message["center"]
                radius = message["radius"]
                _, conflicts = self.map.add_solid_cylindrical_no_fly_zone(
                    center_lat_deg=center["lat"],
                    center_lon_deg=center["lon"],
                    radius_meters=radius,
                    zone_name=zone_name,
                    buffer_distance_meters=settings.settings.map.buffer_distance_meters,
                    planned_paths_dict=self.planned_paths,
                    boundary_thickness_grids=settings.settings.map.boundary_thickness,
                )
            elif "coords" in message:
                # 处理多边形禁飞区
                coords = message["coords"]
                path_data = [(point["lat"], point["lng"], 0) for point in coords]
                # 获取安全缓冲区距离（如果有）
                # buffer_distance = message.get("buffer_distance", 300)
                _, conflicts = self.map.add_hollow_polygonal_no_fly_zone(
                    polygon_vertices=path_data,
                    zone_name=zone_name,
                    offset_meters=settings.settings.map.buffer_distance_meters,
                    boundary_thickness_grids=settings.settings.map.boundary_thickness,
                    planned_paths_dict=MessageHandler.planned_paths,
                )
            else:
                raise KeyError("缺少必要参数: 需要 'center'和'radius' 或 'coords'")

            # # 检查现有路径冲突并处理
            # modified_requests = self._check_existing_paths_conflicts(conflicts)

            # # 处理冲突的路径
            # for modified_request in modified_requests:
            #     try:
            #         # 解析起飞点
            #         start_point = self.parse_point(modified_request["take_off_point"])
            #         end_point = self.parse_point(modified_request["landing_point"])
            #         begin_time = self.parse_time(modified_request["begin_time"])

            #         # 从占用图中删除旧路径
            #         self.occupancy_map.remove_agent(modified_request["flight_id"])

            #         # 重新规划路径
            #         path, path_length = self._replan_path(
            #             modified_request["flight_id"],
            #             start_point,
            #             end_point,
            #             float(modified_request["fly_height"]),
            #             begin_time,
            #             modified_request["current_node_index"],
            #         )

            #         # 合并原有路径和新路径
            #         path = (
            #             modified_request["path"][
            #                 : modified_request["current_node_index"]
            #             ]
            #             + path
            #         )

            #         # 计算结束时间
            #         end_time = begin_time + len(path)
            #         end_time_str = datetime.fromtimestamp(end_time).strftime(
            #             "%Y-%m-%d %H:%M:%S"
            #         )

            #         # 更新路径存储
            #         modified_request["path"] = path
            #         self.planned_paths[modified_request["flight_id"]] = modified_request

            #         if needsend:
            #             # 发送重规划响应
            #             self._send_response(
            #                 request=modified_request,
            #                 new_data={
            #                     "planned_path_points": path,
            #                     "end_time": end_time_str,
            #                     "risk_changeroute": True,
            #                     "risk_reason": "重新规划路径（禁飞区冲突）",
            #                 },
            #                 response_topic=self.response_topic_sora,
            #             )

            #     except Exception as e:
            #         logger.error(f"重新规划路径失败: {str(e)}")
            #         if needsend:
            #             self._send_response(
            #                 request=modified_request,
            #                 new_data={
            #                     "risk_state": False,
            #                     "risk_reason": f"重新规划路径失败: {str(e)}",
            #                 },
            #                 response_topic=self.response_topic,
            #             )
            # logger.info(f"添加禁飞区成功: {zone_name}")
            return conflicts, None

        except KeyError as e:
            error_msg = f"缺少必要参数: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
        except MapError as e:
            error_msg = f"添加禁飞区失败: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
        except Exception as e:
            error_msg = f"处理禁飞区消息时出错: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    def _check_existing_paths_conflicts(self, conflicts: dict) -> List[Dict]:
        """检查所有已规划路径与禁飞区的冲突情况"""
        modified_requests = []
        current_time = time.time()

        for agent_id, conflict_info in conflicts.items():
            flight_info = self.planned_paths.get(agent_id)
            if not flight_info:
                continue

            segments = conflict_info["segments"]
            if not segments:
                continue

            modified_info = flight_info.copy()

            # 查找当前飞行点的索引
            current_node_index = -1
            for i, point in enumerate(flight_info["path"]):
                if point.get("time", point.get("t", 0)) <= current_time:
                    current_node_index = i

            # 还未起飞
            if current_node_index == -1:
                modified_info["current_node_index"] = current_node_index
                modified_requests.append(modified_info)
                logger.info(f"航班{agent_id}还未起飞，需要重新规划路径")
                continue

            # 遍历冲突段
            for segment in segments:
                start_idx = segment["start_idx"]
                end_idx = segment["end_idx"]

                # 还未到达冲突区
                if current_node_index < start_idx:
                    modified_info["current_node_index"] = current_node_index + 1
                    new_start = flight_info["path"][current_node_index + 1]
                    modified_info["take_off_point"] = (
                        f"{new_start['lng']},{new_start['lat']},{new_start['height']}"
                    )
                    modified_info["begin_time"] = time.strftime(
                        "%Y-%m-%d %H:%M:%S",
                        time.localtime(new_start.get("time", new_start.get("t"))),
                    )
                    modified_requests.append(modified_info)
                    logger.info(f"航班{agent_id}已经起飞，但未进入禁飞区，重新规划路径")
                    break

                # 在冲突区前半段
                elif current_node_index < (start_idx + end_idx) // 2:
                    start_point = segment["start"]
                    modified_info["current_node_index"] = start_idx
                    modified_info["take_off_point"] = (
                        f"{start_point['lng']},{start_point['lat']},{start_point['height']}"
                    )
                    modified_info["begin_time"] = time.strftime(
                        "%Y-%m-%d %H:%M:%S",
                        time.localtime(start_point.get("time", start_point.get("t"))),
                    )
                    modified_requests.append(modified_info)
                    logger.info(f"航班{agent_id}在冲突区前半段，从起点重新规划")
                    break

                # 在冲突区后半段，检查是否有后续冲突段
                else:
                    has_next_conflict = any(
                        next_seg["start_idx"] > end_idx for next_seg in segments
                    )
                    if has_next_conflict:
                        end_point = segment["end"]
                        modified_info["current_node_index"] = end_idx
                        modified_info["take_off_point"] = (
                            f"{end_point['lng']},{end_point['lat']},{end_point['height']}"
                        )
                        modified_info["begin_time"] = time.strftime(
                            "%Y-%m-%d %H:%M:%S",
                            time.localtime(end_point.get("time", end_point.get("t"))),
                        )
                        modified_requests.append(modified_info)
                        logger.info(
                            f"航班{agent_id}在冲突区后半段且有后续冲突，从终点重新规划"
                        )
                        break
                    else:
                        logger.info(
                            f"航班{agent_id}在冲突区后半段且无后续冲突，继续执行"
                        )

        return modified_requests

    def _replan_path(
        self,
        flight_id: str,
        start_point: Dict,
        end_point: Dict,
        min_cruise_alt: float,
        begin_time: int,
        start_index: int,
    ) -> Tuple[List, int]:
        """重新规划路径"""
        relative_min_cruise_alt = self.grid_converter.height_to_relative(min_cruise_alt)

        # 转换成网格坐标
        start_grid = self.grid_converter.geo_to_relative(
            start_point["lat"],
            start_point["lon"],
            start_point["alt"],
        )
        end_grid = self.grid_converter.geo_to_relative(
            end_point["lat"],
            end_point["lon"],
            end_point["alt"],
        )

        # 规划路线
        solution, error = self.planner.solve(
            [start_grid],
            [end_grid],
            relative_min_cruise_alt,
            agent_ids=[flight_id],
            start_times=[begin_time],
        )

        if not solution:
            raise Exception(f"路径规划失败: {error}")

        path_grid = solution[flight_id][0]

        # 如果规划成功，将新路径添加到占用图
        if path_grid:
            self.occupancy_map.add_path(path_grid, flight_id)

        # 转换回地理坐标
        path_geo = []
        for i, node in enumerate(path_grid):
            coords = self.grid_converter.relative_to_geo(node.y, node.x, node.z)
            path_geo.append(
                {
                    "lat": coords["lat"],
                    "lng": coords["lon"],
                    "height": coords["alt"],
                    "index": start_index + i,
                }
            )

        return path_geo, len(path_geo)
