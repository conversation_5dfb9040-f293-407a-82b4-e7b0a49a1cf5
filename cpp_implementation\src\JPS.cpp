#include "JPS.h"
#include <cmath>     // 用于数学函数：fabs、std::round、std::sqrt等
#include <algorithm> // 用于算法函数：std::min、std::max、std::sort、std::reverse
#include <sstream>   // 用于字符串流处理：std::ostringstream
#include <set>       // 用于std::set存储不重复的邻居方向
#include <vector>
#include <queue> // 用于std::priority_queue在_find_cruise_path中的实现

// GridNode3D类的属性说明:
// int lon_idx, y, z;        // 三维网格坐标
// long long t;        // 时间戳
// double g, h, f;     // A*算法的代价值：g(起点到当前点代价)、h(当前点到终点预估代价)、f(总代价)
// GridNode3D* parent; // 父节点指针，用于路径回溯
// int jump_step;      // 跳跃步长
// bool need_sight;    // 是否需要视线检查
// 构造函数形式：GridNode3D(int lon_idx, int lat_idx, int z, long long time = 0, double g_val = 0.0)

// 坐标系转换说明：
// 1. 坐标系转换：Python的(y, x, z)对应C++的(x, y, z)
// 注意：在Python中，y对应height，x对应width；而在C++中，x对应width，y对应height
// 为了更清晰，方向向量在C++中也使用(dy, dx, dz)表示，与Python保持一致
// 2. 点坐标转换：
//    - Python格式：(py_y, py_x, py_z)
//    - C++格式：Point3D和GridNode3D使用(cpp_x, cpp_y, cpp_z)
// 3. 映射关系：
//    cpp_x = py_x（Python的x对应C++的x）
//    cpp_y = py_y（Python的y对应C++的y）
//    cpp_z = py_z（Python的z对应C++的z）

const std::vector<JPS::Direction3D> JPS::DIRECTION_PRIORITIES = {
    // 第一优先级：XY平面基本方向（水平移动）
    // 虽然C++中存储为{dx, dy, dz}，但为了清晰，我们将其视为{dy, dx, dz}
    {1, 0, 0},  // 向右移动，对应Python中的(0, 1, 0)，表示dlat=0, dlon=1, dalt=0
    {-1, 0, 0}, // 向左移动，对应Python中的(0, -1, 0)，表示dlat=0, dlon=-1, dalt=0
    {0, 1, 0},  // 向前移动，对应Python中的(1, 0, 0)，表示dlat=1, dlon=0, dalt=0
    {0, -1, 0}, // 向后移动，对应Python中的(-1, 0, 0)，表示dlat=-1, dlon=0, dalt=0
    // 第二优先级：XY平面对角线移动（斜向移动）
    {1, 1, 0},   // 右前移动，对应Python中的(1, 1, 0)，表示dlat=1, dlon=1, dalt=0
    {-1, 1, 0},  // 左前移动，对应Python中的(1, -1, 0)，表示dlat=-1, dlon=1, dalt=0
    {1, -1, 0},  // 右后移动，对应Python中的(-1, 1, 0)，表示dlat=1, dlon=-1, dalt=0
    {-1, -1, 0}, // 左后移动，对应Python中的(-1, -1, 0)，表示dlat=-1, dlon=-1, dalt=0
    // 第三优先级：Z轴垂直移动
    {0, 0, 1}, // 垂直向上，对应Python中的(0, 0, 1)，表示dlat=0, dlon=0, dalt=1
    {0, 0, -1} // 垂直向下，对应Python中的(0, 0, -1)，表示dlat=0, dlon=0, dalt=-1
};

const std::map<JPS::Direction3D, std::vector<JPS::Direction3D>> JPS::FORCED_NEIGHBOR_DIRS = {
    // 坐标系转换说明:
    // Python坐标 (dy, dx, dz) 转换为 C++坐标 (dx, dy, dz)
    // 但为了清晰，我们在注释中将C++坐标也表示为 (dy, dx, dz)

    // 向后移动：Python (-1,0,0) [py_dlat=-1] -> C++ (0,-1,0) [cpp_dlat=-1]
    {{0, -1, 0}, {
                     // 沿-Y轴向后移动时的强制邻居检查方向
                     {1, 0, 0},  // 检查右侧 (对应Python的(0,1,0))
                     {-1, 0, 0}, // 检查左侧 (对应Python的(0,-1,0))
                     {0, 0, 1},  // 检查上方 (对应Python的(0,0,1))
                     {0, 0, -1}  // 检查下方 (对应Python的(0,0,-1))
                 }},
    // 向前移动：Python (1,0,0) [py_dlat=1] -> C++ (0,1,0) [cpp_dlat=1]
    {{0, 1, 0}, {
                    // 沿+Y轴向前移动时的强制邻居检查方向
                    {1, 0, 0},  // 检查右侧
                    {-1, 0, 0}, // 检查左侧
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}  // 检查下方
                }},
    // 向左移动：Python (0,-1,0) [py_dlon=-1] -> C++ (-1,0,0) [cpp_dlon=-1]
    {{-1, 0, 0}, {
                     // 沿-X轴向左移动时的强制邻居检查方向
                     {0, 1, 0},  // 检查前方 (对应Python的(1,0,0))
                     {0, -1, 0}, // 检查后方 (对应Python的(-1,0,0))
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}  // 检查下方
                 }},
    // 向右移动：Python (0,1,0) [py_dlon=1] -> C++ (1,0,0) [cpp_dlon=1]
    {{1, 0, 0}, {
                    // 沿+X轴向右移动时的强制邻居检查方向
                    {0, 1, 0},  // 检查前方
                    {0, -1, 0}, // 检查后方
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}  // 检查下方
                }},
    // 向下移动：Python (0,0,-1) -> C++ (0,0,-1)
    {{0, 0, -1}, {
                     // 沿-Z轴向下移动时的强制邻居检查方向
                     {0, 1, 0},  // 检查前方
                     {0, -1, 0}, // 检查后方
                     {1, 0, 0},  // 检查右侧
                     {-1, 0, 0}  // 检查左侧
                 }},
    // 向上移动：Python (0,0,1) -> C++ (0,0,1)
    {{0, 0, 1}, {
                    // 沿+Z轴向上移动时的强制邻居检查方向
                    {0, 1, 0},  // 检查前方
                    {0, -1, 0}, // 检查后方
                    {1, 0, 0},  // 检查右侧
                    {-1, 0, 0}  // 检查左侧
                }},
    // 对角线方向移动：
    // 右前方向：Python (1,1,0) [py_dlat=1,py_dlon=1] -> C++ (1,1,0) [cpp_dlon=1,cpp_dlat=1]
    {{1, 1, 0}, {
                    // 右前方向移动时的强制邻居检查方向
                    {0, 0, 1},  // 检查上方
                    {0, 0, -1}, // 检查下方
                    {0, 1, 0},  // 检查前向分量 (对应Python的(1,0,0))
                    {1, 0, 0}   // 检查右向分量 (对应Python的(0,1,0))
                }},
    // 左前方向：Python (1,-1,0) [py_dlat=1,py_dlon=-1] -> C++ (-1,1,0) [cpp_dlon=-1,cpp_dlat=1]
    {{-1, 1, 0}, {
                     // 左前方向移动时的强制邻居检查方向
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}, // 检查下方
                     {0, 1, 0},  // 检查前向分量
                     {-1, 0, 0}  // 检查左向分量
                 }},
    // 右后方向：Python (-1,1,0) [py_dlat=-1,py_dlon=1] -> C++ (1,-1,0) [cpp_dlon=1,cpp_dlat=-1]
    {{1, -1, 0}, {
                     // 右后方向移动时的强制邻居检查方向
                     {0, 0, 1},  // 检查上方
                     {0, 0, -1}, // 检查下方
                     {0, -1, 0}, // 检查后向分量
                     {1, 0, 0}   // 检查右向分量
                 }},
    // 左后方向：Python (-1,-1,0) [py_dlat=-1,py_dlon=-1] -> C++ (-1,-1,0) [cpp_dlon=-1,cpp_dlat=-1]
    {{-1, -1, 0}, {
                      // 左后方向移动时的强制邻居检查方向
                      {0, 0, 1},  // 检查上方
                      {0, 0, -1}, // 检查下方
                      {0, -1, 0}, // 检查后向分量
                      {-1, 0, 0}  // 检查左向分量
                  }}};

JPS::JPS(Map3D *map_data,
         OccupancyMap *occupancy_map_data,
         double takeoff_speed,    // 起飞速度
         double cruise_speed,     // 巡航速度
         double landing_speed,    // 降落速度
         int max_steps,           // 最大步数限制
         bool need_smooth,        // 是否需要路径平滑
         double smoothness,       // 平滑度参数
         double heuristic_weight, // 启发式函数权重
         int jump_step_size)      // 跳跃步长（原vertical_look_steps）
    : map_(map_data),
      occupancy_map_(occupancy_map_data),
      lon_size_(0), lat_size_(0), alt_size_(0),
      takeoff_speed_t_(0.0), cruise_speed_t_(0.0), landing_speed_t_(0.0),
      max_steps_(max_steps),
      need_smooth_(need_smooth),
      smoothness_(smoothness),
      heuristic_weight_(heuristic_weight),    // 初始化启发式权重
      default_jump_step_size_(jump_step_size) // 初始化默认跳跃步长
{
    if (!map_data)
    {
        throw std::invalid_argument("地图数据不能为空");
    }
    lon_size_ = map_->get_lon_size();
    lat_size_ = map_->get_lat_size();
    alt_size_ = map_->get_alt_size();

    if (takeoff_speed <= 0 || cruise_speed <= 0 || landing_speed <= 0)
    {
        throw std::invalid_argument("速度值必须为正数");
    }
    if (max_steps <= 0)
    {
        throw std::invalid_argument("最大步数必须为正数");
    }
    if (smoothness < 0)
    {
        throw std::invalid_argument("平滑度参数不能为负数");
    }
    if (heuristic_weight_ <= 0)
    {
        throw std::invalid_argument("启发式函数权重必须为正数");
    }
    if (default_jump_step_size_ < 1)
    {
        throw std::invalid_argument("跳跃步长必须大于等于1");
    }

    // 计算时间步长（时间 = 距离/速度，所以时间步长 = 1/速度）
    takeoff_speed_t_ = 1.0 / takeoff_speed; // 起飞阶段时间步长
    cruise_speed_t_ = 1.0 / cruise_speed;   // 巡航阶段时间步长
    landing_speed_t_ = 1.0 / landing_speed; // 降落阶段时间步长
}

std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
JPS::_vertical_takeoff(
    const Point3D &start_pos_xyz, // 起始位置(lon_idx, lat_idx, alt_idx)
    int min_height_z,             // 起飞目标高度
    const std::string &agent_id,  // 智能体ID
    long long current_time)       // 当前时间
// const std::vector<Constraint>& constraints) // 约束条件（预留）
{
    std::vector<GridNode3D> path;               // 存储垂直起飞路径
    long long time_at_current_z = current_time; // 当前高度的时间戳

    // 提取起始位置的三维坐标
    // 直接解构tuple，避免多次函数调用
    auto [start_lon_idx, start_lat_idx, start_alt_idx] = start_pos_xyz;

    // 参考Python实现:
    // step_size = 5
    // heights = list(range(start[2], min_height + 1, step_size))
    // if min_height not in heights: heights.append(min_height)
    // 这里min_height_z是目标高度，start_z是当前高度
    // 需要从start_z逐步上升到min_height_z

    const int jump_step_size = default_jump_step_size_; // 垂直方向的步进大小
    std::vector<int> heights_to_check;                  // 需要检查的高度列表

    // 如果已经达到或超过最小高度要求
    if (start_alt_idx >= min_height_z)
    {
        // 已在目标高度或更高位置，仅添加当前位置作为路径点
        // 注意：虽然这种情况通常应在调用_vertical_takeoff前处理
        // 因为_vertical_takeoff通常在start[2] < min_height时调用
        // 但为了安全起见，如果遇到这种情况，直接返回起始点
        Point3D current_pt = {start_lon_idx, start_lat_idx, start_alt_idx};
        auto validity = _is_valid_position(current_pt, time_at_current_z, min_height_z, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "起飞位置无效: " + validity.second.value_or("")};
        }
        path.emplace_back(start_lon_idx, start_lat_idx, start_alt_idx, time_at_current_z);
        return {path, std::nullopt};
    }

    // 生成从起始高度到目标高度的高度序列
    for (int z = start_alt_idx; z < min_height_z; z += jump_step_size)
    {
        heights_to_check.push_back(z);
    }

    // 确保包含目标高度
    if (heights_to_check.empty() || heights_to_check.back() < min_height_z)
    {
        // 处理两种情况：
        // 1. 循环未执行（如start_alt_idx接近min_height_z）
        // 2. 最后一步超过了目标高度
        // 这两种情况都需要确保检查min_height_z本身
        bool min_height_present = false;
        for (int h : heights_to_check)
            if (h == min_height_z)
                min_height_present = true;
        if (!min_height_present)
            heights_to_check.push_back(min_height_z);
    }

    // 处理特殊情况：起始高度就是目标高度（如start_alt_idx = 0, min_height = 0）
    if (heights_to_check.empty() && start_alt_idx == min_height_z)
    {
        heights_to_check.push_back(start_alt_idx);
    }

    // 遍历每个高度，生成路径点
    for (int z_coord : heights_to_check)
    {
        Point3D current_level_pos = {start_lon_idx, start_lat_idx, z_coord};

        // 计算时间：
        // - 第一个节点使用current_time
        // - 后续节点的时间递增
        if (!path.empty())
        {
            // 计算从上一个高度到当前高度的时间增量
            int dz = z_coord - path.back().z;
            time_at_current_z += static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * takeoff_speed_t_));
        }
        else if (z_coord != start_alt_idx)
        {
            // 如果不是初始高度，计算从start_alt_idx到当前z_coord的时间
            int dz = z_coord - start_alt_idx;
            time_at_current_z = current_time + static_cast<long long>(std::round(static_cast<double>(std::abs(dz)) * takeoff_speed_t_));
        }

        // 检查位置的有效性（ignore_min_height=true，因为正在上升过程中）
        auto validity = _is_valid_position(current_level_pos, time_at_current_z, min_height_z, agent_id, true);
        if (!validity.first)
        {
            std::ostringstream oss;
            oss << "垂直起飞在高度alt_idlon=" << z_coord
                << "失败。原因: " << validity.second.value_or("未知碰撞");
            return {std::nullopt, oss.str()};
        }

        // 创建并添加当前高度的节点
        path.emplace_back(start_lon_idx, start_lat_idx, z_coord, time_at_current_z);
    }

    // 处理路径为空但不应为空的情况：
    // 1. start_alt_idx < min_height_z但循环未执行
    // 2. min_height_z == start_alt_idx且heights_to_check未正确填充
    // 注意：Python代码确保heights中包含start[2]作为第一个元素
    if (path.empty() && start_alt_idx <= min_height_z)
    {
        // heights_to_check的逻辑可能有问题，至少添加起始点
        Point3D initial_pos = {start_lon_idx, start_lat_idx, start_alt_idx};
        auto validity = _is_valid_position(initial_pos, current_time, min_height_z, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "起始起飞位置无效: " + validity.second.value_or("")};
        }
        path.emplace_back(start_lon_idx, start_lat_idx, start_alt_idx, current_time);
    }

    return {path, std::nullopt};
}

std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
JPS::_vertical_landing(
    const GridNode3D &last_cruise_node, // 巡航阶段最后一个节点
    const Point3D &goal_pos_xyz,        // 目标位置(lon_idx, lat_idx, alt_idx)
    const std::string &agent_id)        // 智能体ID
{
    // 提取起始点和目标点坐标
    int start_alt_idx = last_cruise_node.z;
    // 直接解构目标坐标，避免多次函数调用
    auto [goal_lon_idx, goal_lat_idx, goal_alt_idx] = goal_pos_xyz;

    // 异常检查
    if (start_alt_idx <= goal_alt_idx)
    {
        return {std::nullopt, "降落起始点高度必须大于目标高度"};
    }

    std::vector<GridNode3D> path;
    const int total_descent = start_alt_idx - goal_alt_idx;                                        // 总下降高度
    const int num_steps = (total_descent + default_jump_step_size_ - 1) / default_jump_step_size_; // 向上取整除法，确保最后一步不会过小
    const double height_per_step = total_descent / static_cast<double>(num_steps);                 // 每步下降高度

    // 生成降落轨迹
    for (int i = 0; i <= num_steps; ++i)
    {
        // 计算当前高度
        int current_alt_idx = (i < num_steps) ? start_alt_idx - static_cast<int>(height_per_step * i + 0.5) : goal_alt_idx; // 最后一步直接使用目标高度

        // 计算时间增量（基于已下降的高度）
        long long current_time = last_cruise_node.t +
                                 static_cast<long long>(std::round((start_alt_idx - current_alt_idx) * landing_speed_t_));

        // 检查位置有效性
        Point3D pos = {goal_lon_idx, goal_lat_idx, current_alt_idx};
        auto validity = _is_valid_position(pos, current_time, 0, agent_id, true);
        if (!validity.first)
        {
            return {std::nullopt, "降落位置无效: " + validity.second.value_or("未知错误")};
        }

        path.emplace_back(goal_lon_idx, goal_lat_idx, current_alt_idx, current_time);
    }

    return {path, std::nullopt};
}

// Implementations for _find_cruise_path, _jump, _get_neighbors, etc. will follow.
// find_path will orchestrate these.

// find_path 完整实现 - 路径规划主函数
// 将路径规划分为三个阶段：垂直起飞、水平巡航和垂直降落
std::tuple<
    std::optional<std::vector<GridNode3D>>,      // 完整路径
    std::optional<std::vector<GridNode3D>>,      // 转弯点路径
    std::optional<std::string>>                  // 错误信息
JPS::find_path(const GridNode3D &start_node_ref, // 起始节点
               const GridNode3D &goal_node_ref,  // 目标节点
               int min_height,                   // 最小飞行高度
               const std::string &agent_id,      // 飞行器ID
               long long path_start_time)        // 路径开始时间
{
    // 从节点中提取位置坐标
    Point3D start_pos = _node_to_point3d(start_node_ref);
    Point3D goal_pos = _node_to_point3d(goal_node_ref);

    // 声明变量，用于存储起飞阶段的路径和最后一个节点
    std::vector<GridNode3D> takeoff_path;
    GridNode3D last_takeoff_node(0, 0, 0); // 初始化为默认值，将在后面设置
    long long cruise_start_time = 0;

    // 1. 垂直起飞阶段 - 通过if-else区分不同的起飞情况
    // 解构坐标，避免重复函数调用
    auto [start_lon_idx, start_lat_idx, start_alt_idx] = start_pos;
    auto [goal_lon_idx, goal_lat_idx, goal_alt_idx] = goal_pos;

    if (start_alt_idx >= min_height)
    {
        // 情况1: 起始高度已经满足最小高度要求，不需要垂直起飞
        // 在当前位置和时间创建起始节点
        last_takeoff_node = GridNode3D(
            start_lon_idx,
            start_lat_idx,
            start_alt_idx,
            path_start_time);
        takeoff_path = {last_takeoff_node};
        cruise_start_time = path_start_time;
    }
    else
    {
        // 情况2: 需要垂直起飞
        auto takeoff_result = _vertical_takeoff(start_pos, min_height, agent_id, path_start_time);
        if (!std::get<0>(takeoff_result))
        {
            return {std::nullopt, std::nullopt, "Takeoff failed: " + std::get<1>(takeoff_result).value_or("Unknown error")};
        }

        takeoff_path = std::get<0>(takeoff_result).value();
        if (takeoff_path.empty())
        {
            return {std::nullopt, std::nullopt, "Takeoff path is empty."};
        }

        last_takeoff_node = takeoff_path.back();
        cruise_start_time = last_takeoff_node.t;
    }

    // 2. 巡航阶段 - 共用代码
    auto cruise_result = _find_cruise_path(
        last_takeoff_node,
        {goal_lon_idx, goal_lat_idx, min_height}, // 在最小高度巡航
        min_height,
        agent_id,
        cruise_start_time);

    // 处理巡航结果
    if (!std::get<0>(cruise_result))
    {
        return {std::nullopt, std::nullopt, "Cruise failed: " + std::get<1>(cruise_result).value_or("Unknown error")};
    }

    // 获取巡航路径并继续降落阶段
    std::vector<GridNode3D> cruise_path = std::get<0>(cruise_result).value();

    // 3. 垂直降落阶段
    auto landing_result = _vertical_landing(
        cruise_path.back(),
        goal_pos,
        agent_id);

    if (!std::get<0>(landing_result))
    {
        return {std::nullopt, std::nullopt, "Landing failed: " + std::get<1>(landing_result).value_or("Unknown error")};
    }

    std::vector<GridNode3D> landing_path = std::get<0>(landing_result).value();

    // 确定巡航路径的转弯点
    std::vector<GridNode3D> cruise_turn_path;
    std::vector<int> cruise_turn_indices;

    // 从巡航路径中提取转弯点
    auto [turn_path, turn_indices] = extract_turning_points(cruise_path);
    cruise_turn_path = turn_path;
    cruise_turn_indices = turn_indices;

    // 如果需要，应用平滑处理
    if (need_smooth_)
    {
        auto [smoothed_path, smoothed_turn_path] = moving_average_smooth(
            cruise_path,
            cruise_turn_indices,
            min_height,
            agent_id);

        cruise_path = smoothed_path;
        cruise_turn_path = smoothed_turn_path;
    }

    // 合并所有路径段 - 跳过重复点
    std::vector<GridNode3D> complete_path = takeoff_path;
    // 添加巡航路径(跳过第一个点，因为与起飞路径最后一个点重复)
    complete_path.insert(complete_path.end(), cruise_path.begin() + 1, cruise_path.end());
    // 添加降落路径(跳过第一个点，因为与巡航路径最后一个点重复)
    complete_path.insert(complete_path.end(), landing_path.begin() + 1, landing_path.end());

    // 创建完整的转弯点路径 - 跳过重复点
    std::vector<GridNode3D> complete_turn_path = {takeoff_path.front()}; // 起始点
    // 添加巡航转弯点
    complete_turn_path.insert(complete_turn_path.end(), cruise_turn_path.begin(), cruise_turn_path.end());
    // 添加终点
    complete_turn_path.push_back(landing_path.back());

    return {complete_path, complete_turn_path, std::nullopt};
}

// --- 其他私有方法实现（已存在或待实现的存根）---

std::pair<bool, std::optional<std::string>> JPS::_check_constraints_and_collisions(
    const Point3D &pos, // x, y, z
    long long time,
    const std::string &agent_id)
// const std::vector<Constraint>& constraints)
{

    if (occupancy_map_)
    {
        if (occupancy_map_->check_collision(pos, time)) // Assuming this checks against other agents
        {
            // Python版本的OccupancyMap.check_collision返回(bool, colliding_agent_id)
            // 且JPS检查`if has_collision and colliding_agent != agent_id:`
            // 如果C++版本的OccupancyMap::check_collision不知道当前agent_id来排除自碰撞，
            // 可能需要调整或OccupancyMap需要agent_id参数
            // 目前假设check_collision已处理此问题或这是一般的动态障碍物
            std::ostringstream oss;
            oss << "Dynamic collision detected at (lon=" << std::get<0>(pos) << ", lat=" << std::get<1>(pos) << ", alt=" << std::get<2>(pos)
                << ") at time " << time;
            return {false, oss.str()};
        }
    }
    // 实际约束检查的占位符
    // if (constraints) { ... }
    return {true, std::nullopt};
}

std::pair<bool, std::optional<std::string>> JPS::_is_valid_position(
    const Point3D &pos_xyz, // x, y, z
    long long time,
    int min_height_z,
    const std::string &agent_id,
    // const std::vector<Constraint>& constraints,
    bool ignore_min_height)
{
    // 1. 边界检查
    // pos_xyz格式: (lon_idx, lat_idx, alt_idx)
    if (!map_->is_within_bounds(std::get<0>(pos_xyz), std::get<1>(pos_xyz), std::get<2>(pos_xyz)))
    {
        std::vector<std::string> actual_obstacle_types;
        // 假定ObstacleManager可以提供某点的障碍类型，或从is_traversable推断
        // 简单起见，如果不可通行，就视为障碍物或超出边界
        std::ostringstream oss;
        oss << "Position (lon=" << std::get<0>(pos_xyz) << ", lat=" << std::get<1>(pos_xyz) << ", alt=" << std::get<2>(pos_xyz) << ") "
            << "out of bounds.";
        // TODO: Get specific obstacle types if possible from map_->get_obstacle_manager()
        return {false, oss.str()};
    }
    // 2. 障碍物检查
    if (!map_->is_traversable(pos_xyz))
    {
        std::vector<std::string> actual_obstacle_types;
        // 假定ObstacleManager可以提供某点的障碍类型，或从is_traversable推断
        // 简单起见，如果不可通行，就视为障碍物或超出边界
        std::ostringstream oss;
        oss << "Position (lon=" << std::get<0>(pos_xyz) << ", lat=" << std::get<1>(pos_xyz) << ", alt=" << std::get<2>(pos_xyz) << ") "
            << "is not traversable (no fly zone).";
        // TODO: Get specific obstacle types if possible from map_->get_obstacle_manager()
        return {false, oss.str()};
    }

    // 检查其他约束和航线冲突
    auto base_check = _check_constraints_and_collisions(pos_xyz, time, agent_id); //, constraints);
    if (!base_check.first)
    {
        return base_check;
    }

    // 3. 最小高度检查
    int cpp_z = std::get<2>(pos_xyz);
    if (!ignore_min_height && cpp_z < min_height_z)
    {
        std::ostringstream oss;
        // Assuming GridConverter is accessible for geo conversion if needed for error message
        // GeoCoordinate real_alt_geo = map_->get_converter().grid_to_geographic(0,0,static_cast<float>(cpp_z));
        // GeoCoordinate min_alt_geo = map_->get_converter().grid_to_geographic(0,0,static_cast<float>(min_height_z));
        oss << "Altitude " << cpp_z << " is below minimum cruise altitude " << min_height_z;
        return {false, oss.str()};
    }

    return {true, std::nullopt};
}

double JPS::_octile_distance(const Point3D &p1_xyz, const Point3D &p2_xyz) const
{
    int dx_abs = std::abs(std::get<0>(p1_xyz) - std::get<0>(p2_xyz)); // dx for x-coordinate
    int dy_abs = std::abs(std::get<1>(p1_xyz) - std::get<1>(p2_xyz)); // dy for y-coordinate
    int dz_abs = std::abs(std::get<2>(p1_xyz) - std::get<2>(p2_xyz)); // dz for z-coordinate

    const double D_diag = std::sqrt(2.0); // Cost for diagonal in 2D plane (XY)
    // Octile for XY plane, then add Z movement cost
    double cost_xy = D_diag * static_cast<double>(std::min(dx_abs, dy_abs)) +
                     static_cast<double>(std::abs(dx_abs - dy_abs));
    double cost_z = static_cast<double>(dz_abs); // Z轴移动每个网格单位的代价为1

    return cost_xy + cost_z;
}

// _get_neighbors 实现 - 获取当前节点的邻居节点
// 考虑水平和垂直方向的移动，以及垂直避障
// 坐标系转换：Python的(y, x, z)对应C++的(x, y, z)
std::vector<JPS::NeighborPath> JPS::_get_neighbors(
    const GridNode3D &current_node, // 当前节点
    int min_height,                 // 最小飞行高度
    const std::string &agent_id)    // 飞行器ID
{
    // 优化1: 预分配容器大小，减少动态扩容
    std::vector<NeighborPath> valid_neighbors;
    valid_neighbors.reserve(10); // 预分配合理大小
    std::vector<Direction3D> blocked_directions;
    blocked_directions.reserve(8); // 最多8个水平方向

    // 直接访问节点成员，避免函数调用开销
    const int curr_lon_idx = current_node.x;       // 经度方向坐标
    const int curr_lat_idx = current_node.y;       // 纬度方向坐标
    const int curr_alt_idx = current_node.z;       // 高度方向坐标
    const int jump_step = current_node.jump_step;  // 优化2: 缓存jump_step
    const long long current_time = current_node.t; // 优化3: 缓存当前时间

    const long long next_time = current_time + static_cast<long long>(std::round(cruise_speed_t_));

    // 优化4: 预计算父节点位置，避免重复计算
    int parent_lon_idx = -1, parent_lat_idx = -1, parent_alt_idx = -1;
    bool has_parent = false;
    if (current_node.parent)
    {
        parent_lon_idx = current_node.parent->x;
        parent_lat_idx = current_node.parent->y;
        parent_alt_idx = current_node.parent->z;
        has_parent = true;
    }

    // 优化17: 使用分离的水平方向常量，避免条件检查
    for (size_t i = 0; i < HORIZONTAL_DIR_COUNT; ++i)
    {
        const int dx = HORIZONTAL_DIRECTIONS[i][0];
        const int dy = HORIZONTAL_DIRECTIONS[i][1];
        // dz = 0 对于水平方向，无需检查

        // 优化6: 直接计算新位置，避免重复变量赋值
        const int new_lon_idx = curr_lon_idx + dx * jump_step; // dx实际上是经度方向的变化
        const int new_lat_idx = curr_lat_idx + dy * jump_step; // dy实际上是纬度方向的变化
        const int new_alt_idx = curr_alt_idx;                  // 保持相同的高度层级

        // 优化7: 使用预计算的父节点位置，避免函数调用和tuple访问
        if (has_parent && new_lon_idx == parent_lon_idx && new_lat_idx == parent_lat_idx && new_alt_idx == parent_alt_idx)
        {
            continue; // 如果新位置是父节点则跳过，避免往回走
        }

        const Point3D new_pos = {new_lon_idx, new_lat_idx, new_alt_idx};

        auto validity = _is_valid_position(new_pos, next_time, min_height, agent_id);
        if (!validity.first)
        {
            // 优化8: 使用更高效的字符串检查方式
            if (validity.second)
            {
                const std::string &error_msg = *validity.second;
                if (error_msg.find("out of bounds") != std::string::npos)
                {
                    // 如果超出边界，跳过
                    continue;
                }
                else if (error_msg.find("no fly zone") != std::string::npos)
                {
                    // 如果被阻挡，固定障碍物只能绕行
                    continue;
                }
                else
                {
                    // 遇到其他航线障碍物，记录下来
                    blocked_directions.emplace_back(dx, dy, 0); // 优化9: 使用emplace_back
                }
            }
            else
            {
                // 没有错误信息，默认记录为阻挡
                blocked_directions.emplace_back(dx, dy, 0);
            }
        }
        else
        {
            // 优化10: 直接构造路径，减少临时对象
            NeighborPath path;
            path.reserve(1);                                          // 只有一个点
            path.emplace_back(new_lon_idx, new_lat_idx, new_alt_idx); // 直接构造Point3D
            valid_neighbors.emplace_back(std::move(path));            // 使用移动语义
        }
    }

    // 对被阻挡的水平方向，尝试垂直绕行
    for (const auto &blocked_dir : blocked_directions)
    {
        // 优化11: 使用结构化绑定，真正避免std::get调用
        const auto [dx, dy, dz_unused] = blocked_dir;

        // 优化12: 预计算步长相关值，避免重复计算
        const int half_step = default_jump_step_size_ / 2;
        const int max_step = default_jump_step_size_;
        const int step_increment = (half_step > 0) ? half_step : 1; // 确保步长增量至少为1

        // Try vertical steps (up to default_jump_step_size_)
        // Create a path with intermediate points
        NeighborPath path;
        path.reserve(4); // 预分配空间，最多2个上升点 + 2个水平点

        for (int step = step_increment; step <= max_step; step += step_increment)
        {
            // 首先检查是否可以从当前位置向上移动
            const int up_alt_idx = curr_alt_idx + step;

            const Point3D up_pos = {curr_lon_idx, curr_lat_idx, up_alt_idx};
            const long long next_up_time = current_time + static_cast<long long>(std::round(cruise_speed_t_ * step));

            auto validity = _is_valid_position(up_pos, next_up_time, min_height, agent_id);
            if (!validity.first)
            {
                break;
            }

            // 现在检查是否可以在较高层级水平移动
            const int new_horizontal_lon_idx = curr_lon_idx + dx * jump_step; // 使用缓存的jump_step
            const int new_horizontal_lat_idx = curr_lat_idx + dy * jump_step;

            const Point3D new_pos = {new_horizontal_lon_idx, new_horizontal_lat_idx, up_alt_idx};
            const long long horizontal_time = next_up_time + static_cast<long long>(std::round(cruise_speed_t_ * jump_step));
            validity = _is_valid_position(new_pos, horizontal_time, min_height, agent_id);
            if (validity.first)
            {
                // 优化13: 使用emplace_back直接构造
                path.emplace_back(curr_lon_idx, curr_lat_idx, up_alt_idx);                     // First go up
                path.emplace_back(new_horizontal_lon_idx, new_horizontal_lat_idx, up_alt_idx); // Then go horizontally
                valid_neighbors.emplace_back(std::move(path));                                 // 使用移动语义
                break;                                                                         // 找到有效高度，无需继续向上
            }
            else
            {
                path.emplace_back(curr_lon_idx, curr_lat_idx, up_alt_idx);
            }
        }
    }

    // 优化18: 使用分离的垂直方向常量，避免条件检查
    for (size_t i = 0; i < VERTICAL_DIR_COUNT; ++i)
    {
        const int dz = VERTICAL_DIRECTIONS[i][2];
        // dx = 0, dy = 0 对于垂直方向，无需检查

        // Calculate new position
        const int new_alt_idx = curr_alt_idx + dz;

        // 检查新位置是否在地图边界内
        if (new_alt_idx < 0 || new_alt_idx >= alt_size_)
        {
            continue;
        }

        const Point3D new_pos = {curr_lon_idx, curr_lat_idx, new_alt_idx};

        // 优化15: 使用预计算的时间值
        const long long vertical_time = current_time + static_cast<long long>(std::round(cruise_speed_t_ * jump_step));
        auto validity = _is_valid_position(new_pos, vertical_time, min_height, agent_id);
        if (validity.first)
        {
            // 优化16: 直接构造并移动路径
            NeighborPath path;
            path.reserve(1);
            path.emplace_back(curr_lon_idx, curr_lat_idx, new_alt_idx);
            valid_neighbors.emplace_back(std::move(path));
        }
    }

    return valid_neighbors;
}

// _jump 函数实现 - 跳点搜索的核心函数
// 从当前节点沿指定方向搜索，直到找到跳点或无法继续
// 跳点定义：1. 目标点 2. 有强制邻居的点 3. 起始点
std::optional<GridNode3D> JPS::_jump(
    const GridNode3D &initial_node_param, // 当前节点
    const GridNode3D &parent_node,        // 父节点（用于确定搜索方向）
    const Point3D &goal_pos,              // 目标位置
    int min_height,                       // 最小飞行高度
    const std::string &agent_id,          // 飞行器ID
    int jump_step)                        // 跳跃步长
// const std::vector<Constraint>& constraints,
// int vertical_look_steps)
{
    GridNode3D current_node = initial_node_param; // 创建可修改的局部副本

    // 空值检查
    if (current_node.x < 0 || current_node.y < 0 || current_node.z < 0)
    {
        return std::nullopt;
    }

    // 优化29: 直接提取坐标，避免函数调用和tuple访问
    const int curr_lon_idx = current_node.x;
    const int curr_lat_idx = current_node.y;
    const int curr_alt_idx = current_node.z;

    const auto [goal_lon_idx, goal_lat_idx, goal_alt_idx] = goal_pos; // 优化30: 使用结构化绑定

    // 快速目标检查
    if (curr_lon_idx == goal_lon_idx && curr_lat_idx == goal_lat_idx)
    {
        return current_node;
    }

    // 优化31: 缓存父节点坐标
    const int parent_lon_idx = parent_node.x;
    const int parent_lat_idx = parent_node.y;

    const int offset_x_val = curr_lon_idx - parent_lon_idx;
    const int offset_y_val = curr_lat_idx - parent_lat_idx;

    const int dx = (offset_x_val == 0) ? 0 : (jump_step * offset_x_val / std::abs(offset_x_val));
    const int dy = (offset_y_val == 0) ? 0 : (jump_step * offset_y_val / std::abs(offset_y_val));
    const int dz = 0; // 水平移动

    // 优化32: 预计算移动成本和时间增量
    const double per_path_cost = (dx != 0 && dy != 0) ? 1.414 * jump_step : 1.0 * jump_step;
    const long long time_increment = static_cast<long long>(std::round(cruise_speed_t_ * jump_step));

    // 优化33: 预分配路径容器，估算合理大小
    std::vector<GridNode3D> intermediate_path;
    intermediate_path.reserve(max_steps_ / jump_step + 2);
    intermediate_path.push_back(current_node);

    // 优化34: 直接计算下一个位置的坐标
    int next_lon_idx = curr_lon_idx + dx;
    int next_lat_idx = curr_lat_idx + dy;
    int next_alt_idx = curr_alt_idx + dz;

    // 优化35: 使用预计算的时间增量
    long long next_t = current_node.t + time_increment;
    double next_g = current_node.g + per_path_cost;

    // 创建下一个节点
    GridNode3D next_node(next_lon_idx, next_lat_idx, next_alt_idx, next_t, next_g);
    next_node.jump_step = jump_step;
    next_node.parent = std::make_shared<GridNode3D>(current_node); // Use make_shared

    int steps = jump_step;

    while (true)
    {
        // 优化36: 直接构造Point3D，避免函数调用
        const Point3D next_pos = {next_lon_idx, next_lat_idx, next_alt_idx};
        auto validity = _is_valid_position(next_pos, next_t, min_height, agent_id);

        // 如果下一个位置不可达，尝试向上查看
        if (!validity.first && dz == 0) // 只在水平移动时尝试向上
        {
            // 优化37: 使用更高效的字符串检查
            if (validity.second)
            {
                const std::string &error_msg = *validity.second;
                if (error_msg.find("out of bounds") != std::string::npos)
                {
                    // Cannot jump further, return the last valid node in the current jump direction.
                    GridNode3D result = intermediate_path.back();
                    result.need_sight = (steps == jump_step);
                    return result;
                }
                else if (error_msg.find("no fly zone") != std::string::npos)
                {
                    GridNode3D result = intermediate_path.back();
                    result.need_sight = false; // Hit a no-fly zone, no line of sight check needed from here.
                    return result;
                }
            }

            bool found_vertical_path = false;
            // 优化38: 直接获取当前中间节点信息，避免函数调用
            const GridNode3D &current_intermediate = intermediate_path.back();
            const int intermediate_lon_idx = current_intermediate.x;
            const int intermediate_lat_idx = current_intermediate.y;
            const int intermediate_alt_idx = current_intermediate.z;
            const long long current_intermediate_time = current_intermediate.t;
            const double current_intermediate_g = current_intermediate.g;

            // 优化39: 预计算步长增量，避免重复计算
            const int step_increment = std::max(1, jump_step / 2);

            for (int step_v = step_increment; step_v <= jump_step; step_v += step_increment)
            {
                // 优化40: 直接计算垂直位置，避免tuple访问
                const int vertical_alt_idx = intermediate_alt_idx + step_v;

                // 检查是否超出地图范围
                if (vertical_alt_idx >= alt_size_)
                {
                    // Cannot go further up this way. Return last valid node.
                    GridNode3D result = intermediate_path.back();
                    result.need_sight = true; // Tried to look up, implies sight needed if this was a jump point
                    return result;
                }

                const Point3D vertical_pos = {intermediate_lon_idx, intermediate_lat_idx, vertical_alt_idx};
                const long long up_time = current_intermediate_time + static_cast<long long>(std::round(cruise_speed_t_ * step_v));

                // 检查垂直位置是否可达
                auto vertical_valid = _is_valid_position(vertical_pos, up_time, min_height, agent_id);

                if (vertical_valid.first)
                {
                    // 优化41: 直接构造垂直上升节点，避免tuple访问
                    auto up_node_ptr = std::make_shared<GridNode3D>(
                        intermediate_lon_idx,
                        intermediate_lat_idx,
                        vertical_alt_idx,
                        up_time);
                    up_node_ptr->parent = std::make_shared<GridNode3D>(current_intermediate);
                    up_node_ptr->g = current_intermediate_g + 1.0 * step_v; // Cost for vertical step

                    intermediate_path.push_back(*up_node_ptr); // Push a copy

                    // 优化42: 直接构造水平位置，避免tuple访问
                    const Point3D horizontal_pos = {next_lon_idx, next_lat_idx, vertical_alt_idx};
                    const long long horizontal_time = up_time + time_increment; // 使用预计算的时间增量

                    auto horizontal_valid = _is_valid_position(horizontal_pos, horizontal_time, min_height, agent_id);

                    if (!horizontal_valid.first)
                    {
                        intermediate_path.pop_back(); // Remove the up_node if horizontal move fails
                        continue;                     // Try next vertical step_v
                    }

                    // 优化43: 直接构造水平移动节点
                    next_node = GridNode3D(next_lon_idx, next_lat_idx, vertical_alt_idx, horizontal_time);
                    next_node.parent = up_node_ptr;               // Parent is the successful up_node
                    next_node.g = up_node_ptr->g + per_path_cost; // per_path_cost for horizontal jump
                    next_node.jump_step = jump_step;

                    // 优化44: 更新next_t和next_z以保持一致性
                    next_t = horizontal_time;
                    next_alt_idx = vertical_alt_idx;

                    intermediate_path.push_back(next_node); // Push the new successful next_node

                    found_vertical_path = true;
                    break; // Found a vertical detour, break from vertical step search
                }
            }

            // 如果没有找到垂直路径，结束搜索 for this jump direction
            if (!found_vertical_path)
            {
                GridNode3D result = intermediate_path.back(); // Return the last valid node before trying vertical
                result.need_sight = false;                    // Failed to find vertical detour
                return result;
            }
            // If found_vertical_path, next_node is now updated and loop continues with this new next_node
        }
        else if (validity.first) // Original next_node was valid
        {
            // 正常情况，将节点添加到路径中
            intermediate_path.push_back(next_node);
        }
        else // Original next_node was invalid and it was not a horizontal jump (dz != 0), or some other unhandled case
        {
            GridNode3D result = intermediate_path.back(); // Should be current_node before this invalid next_node
            result.need_sight = true;                     // Unclear why it failed, assume sight needed
            return result;
        }

        // current_node for the next iteration is the last one added to intermediate_path
        current_node = intermediate_path.back();

        // 优化45: 直接比较坐标，避免tuple访问
        if (current_node.x == goal_lon_idx && current_node.y == goal_lat_idx && current_node.z == goal_alt_idx)
        {
            return current_node;
        }

        // Check for forced neighbors or if current_node is a jump point
        // This logic is missing from the provided C++ _jump, but is crucial for JPS.
        // For now, we assume _jump continues straight or detours vertically.
        // If JPS specific jump point conditions are met (e.g., forced neighbor),
        // current_node should be returned.

        // 更新下一个位置 for the next iteration of the straight jump
        // dx, dy are the fixed direction components for this jump call
        next_lon_idx = current_node.x + dx;
        next_lat_idx = current_node.y + dy;
        next_alt_idx = current_node.z; // Continue horizontally unless dz was part of the direction (original code dalt=0)

        // 优化46: 使用预计算的时间增量
        next_t = current_node.t + time_increment;
        const double new_next_g = current_node.g + per_path_cost;

        // 创建新的下一个节点 for the next iteration
        next_node = GridNode3D(next_lon_idx, next_lat_idx, next_alt_idx, next_t, new_next_g);
        next_node.parent = std::make_shared<GridNode3D>(current_node); // Parent is the current_node of this iteration
        next_node.jump_step = jump_step;                               // Keep original jump_step for this jump sequence

        steps += jump_step;

        // 优化47: 直接计算距离，避免函数调用和Point3D构造
        const double curr_dx = static_cast<double>(current_node.x - goal_lon_idx);
        const double curr_dy = static_cast<double>(current_node.y - goal_lat_idx);
        const double curr_dz = static_cast<double>(current_node.z - goal_alt_idx);
        const double current_dist_to_goal = std::sqrt(std::max(std::abs(curr_dx), std::abs(curr_dy)) * std::max(std::abs(curr_dx), std::abs(curr_dy)) + curr_dz * curr_dz);

        const double next_dx = static_cast<double>(next_lon_idx - goal_lon_idx);
        const double next_dy = static_cast<double>(next_lat_idx - goal_lat_idx);
        const double next_dz = static_cast<double>(next_alt_idx - goal_alt_idx);
        const double next_potential_dist_to_goal = std::sqrt(std::max(std::abs(next_dx), std::abs(next_dy)) * std::max(std::abs(next_dx), std::abs(next_dy)) + next_dz * next_dz);

        if (next_potential_dist_to_goal >= current_dist_to_goal && current_dist_to_goal > 0.1 /*not at goal*/)
        {
            // Not getting closer (or moving away), current_node might be a jump point or dead end.
            return current_node;
        }
        if (steps > max_steps_) // max_steps_ is a member, not local
        {
            return current_node; // Exceeded max jump length for this segment
        }
    }

    return std::nullopt; // Should be unreachable if loop logic is correct
}

// Heuristic function (as defined in JPS.h, needs implementation if not already)
double JPS::_heuristic(const Point3D &p, const Point3D &goal) const
{
    return heuristic_weight_ * _octile_distance(p, goal);
}

double JPS::_euclidean_distance_squared(const Point3D &p1, const Point3D &p2) const
{
    // 优化28: 使用结构化绑定，避免std::get调用
    const auto [x1, y1, z1] = p1;
    const auto [x2, y2, z2] = p2;

    const double dx = static_cast<double>(x1 - x2);
    const double dy = static_cast<double>(y1 - y2);
    const double dz = static_cast<double>(z1 - z2);
    return dx * dx + dy * dy + dz * dz;
}

// has_line_of_sight 实现 - 检查两点之间是否有直接视线
// 使用Bresenham算法的3D变体来检查两点之间的路径是否可行
std::pair<bool, std::vector<GridNode3D>> JPS::has_line_of_sight(
    const GridNode3D &start_node_los, // 起始节点
    const Point3D &end_pos_los,       // 终点位置
    long long los_start_time,         // 开始时间
    int min_height,                   // 最小飞行高度
    const std::string &agent_id)      // 飞行器ID
// const std::vector<Constraint>& constraints)
{
    // 优化19: 直接提取坐标，避免函数调用和tuple访问
    const int start_lon_idx = start_node_los.x;
    const int start_lat_idx = start_node_los.y;
    const int start_alt_idx = start_node_los.z;
    const int jump_step = start_node_los.jump_step;

    const int end_lon_idx = std::get<0>(end_pos_los);
    const int end_lat_idx = std::get<1>(end_pos_los);
    // const int end_alt_idx = std::get<2>(end_pos_los); // 未使用

    // 优化20: 直接计算距离平方，避免函数调用
    const int dx = end_lon_idx - start_lon_idx;
    const int dy = end_lat_idx - start_lat_idx;
    const double dist_squared = static_cast<double>(dx * dx + dy * dy);

    // 如果点非常接近，无需进行视线检查
    const double threshold = static_cast<double>(jump_step * jump_step);
    if (dist_squared < threshold)
    {
        std::vector<GridNode3D> path;
        path.reserve(1);
        path.push_back(start_node_los);
        return {true, std::move(path)};
    }

    const int steps = std::max(std::abs(dx), std::abs(dy));

    if (steps == 0)
    {
        std::vector<GridNode3D> path;
        path.reserve(1);
        path.push_back(start_node_los);
        return {true, std::move(path)};
    }

    // 计算每步的增量
    const double sx = static_cast<double>(dx) / steps;
    const double sy = static_cast<double>(dy) / steps;

    // 优化21: 预计算时间增量，避免循环中重复计算
    const long long time_increment = static_cast<long long>(std::round(cruise_speed_t_));

    // 优化22: 预分配路径容器，估算大小
    const size_t estimated_path_size = static_cast<size_t>(steps / jump_step) + 2;
    std::vector<GridNode3D> path_points;
    path_points.reserve(estimated_path_size);

    // 添加起始点
    path_points.push_back(start_node_los);

    // 优化23: 缓存上一个位置，避免重复的Point3D构造
    int last_lon_idx = start_lon_idx;
    int last_lat_idx = start_lat_idx;
    long long current_t = los_start_time;

    // 在路径上采样点
    for (int i = jump_step; i <= steps; i += jump_step)
    {
        // 计算当前采样点坐标
        const double x_float = start_lon_idx + sx * i;
        const double y_float = start_lat_idx + sy * i;
        const double z_float = start_alt_idx;

        const int lon_idx = static_cast<int>(x_float + 0.5);
        const int lat_idx = static_cast<int>(y_float + 0.5);

        // 优化24: 直接比较坐标，避免Point3D构造
        if (x == last_lon_idx && y == last_lat_idx)
        {
            continue; // 跳过重复点
        }

        // 更新时间
        current_t += time_increment;

        // 优化25: 直接构造Point3D用于检查
        const Point3D current_pos = {x, y, start_alt_idx};

        // 检查位置有效性
        auto validity = _is_valid_position(current_pos, current_t, min_height, agent_id);
        if (!validity.first)
        {
            return {false, {path_points.back()}};
        }

        // 添加有效点到路径
        path_points.emplace_back(x_float, y_float, z_float, current_t);
        last_lon_idx = x;
        last_lat_idx = y;
    }

    // 优化26: 直接比较坐标，避免std::get调用
    if (last_lon_idx != end_lon_idx || last_lat_idx != end_lat_idx)
    {
        current_t += time_increment;
        path_points.emplace_back(static_cast<float>(end_lon_idx),
                                 static_cast<float>(end_lat_idx),
                                 static_cast<float>(start_alt_idx),
                                 current_t);
    }

    return {true, std::move(path_points)}; // 优化27: 使用移动语义返回
}

// extract_turning_points 实现 - 从路径中提取转弯点
// 通过检查相邻路径段的方向变化来识别转弯点
std::pair<std::vector<GridNode3D>, std::vector<int>> JPS::extract_turning_points(
    const std::vector<GridNode3D> &path) // 输入路径
{
    if (path.empty() || path.size() <= 2)
    {
        return {path, {}}; // 如果路径为空或只有2个点，返回原始路径和空索引
    }

    // 初始化转弯点列表和索引列表
    std::vector<GridNode3D> turning_points;
    std::vector<int> turning_indices;

    // 始终包含起始点
    turning_points.push_back(path[0]);

    // 检查每个点的方向变化
    for (size_t i = 1; i < path.size() - 1; ++i)
    {
        const GridNode3D &prev_node = path[i - 1];
        const GridNode3D &curr_node = path[i];
        const GridNode3D &next_node = path[i + 1];

        // 计算方向向量
        double curr_dir_x = curr_node.x - prev_node.x;
        double curr_dir_y = curr_node.y - prev_node.y;
        double curr_dir_z = curr_node.z - prev_node.z;

        double curr_norm = std::sqrt(curr_dir_x * curr_dir_x + curr_dir_y * curr_dir_y + curr_dir_z * curr_dir_z);
        if (curr_norm < 0.001)
            continue; // 如果线段长度为零则跳过

        // 归一化当前方向向量
        curr_dir_x /= curr_norm;
        curr_dir_y /= curr_norm;
        curr_dir_z /= curr_norm;

        // 计算下一个方向向量
        double next_dir_x = next_node.x - curr_node.x;
        double next_dir_y = next_node.y - curr_node.y;
        double next_dir_z = next_node.z - curr_node.z;

        double next_norm = std::sqrt(next_dir_x * next_dir_x + next_dir_y * next_dir_y + next_dir_z * next_dir_z);
        if (next_norm < 0.001)
            continue; // 如果线段长度为零则跳过

        // 归一化下一个方向向量
        next_dir_x /= next_norm;
        next_dir_y /= next_norm;
        next_dir_z /= next_norm;

        // 检查方向是否改变
        bool direction_changed = false;

        // 检查方向向量的任何分量是否发生变化
        if (std::abs(curr_dir_x - next_dir_x) > 0.001 ||
            std::abs(curr_dir_y - next_dir_y) > 0.001 ||
            std::abs(curr_dir_z - next_dir_z) > 0.001)
        {

            // 计算方向向量之间的平方距离，检查变化是否显著
            double dist_squared = (curr_dir_x - next_dir_x) * (curr_dir_x - next_dir_x) +
                                  (curr_dir_y - next_dir_y) * (curr_dir_y - next_dir_y) +
                                  (curr_dir_z - next_dir_z) * (curr_dir_z - next_dir_z);

            // 使用小阈值确定方向变化是否显著
            if (dist_squared > 0.02)
            {
                direction_changed = true;
            }
        }

        // 如果方向改变，将当前点添加为转弯点
        if (direction_changed)
        {
            turning_points.push_back(curr_node);
            turning_indices.push_back(static_cast<int>(i)); // 记录索引
        }
    }

    // 始终包含终点
    turning_points.push_back(path.back());

    return {turning_points, turning_indices};
}

// moving_average_smooth 实现 - 使用移动平均法平滑路径
// 对路径中的点应用加权移动平均，同时保持转弯点的特性
std::pair<std::vector<GridNode3D>, std::vector<GridNode3D>> JPS::moving_average_smooth(
    const std::vector<GridNode3D> &path,     // 输入路径
    const std::vector<int> &turning_indices, // 转弯点索引
    int min_height,                          // 最小飞行高度
    const std::string &agent_id)             // 飞行器ID
// const std::vector<Constraint>& constraints)
{
    if (path.size() < 3)
    {
        return {path, path}; // 点数不足，无法平滑
    }

    // 根据平滑参数计算窗口大小
    int window_size = std::max(3, static_cast<int>(smoothness_));
    if (window_size % 2 == 0)
    {
        window_size += 1; // 确保窗口大小为奇数
    }

    int half_window = window_size / 2;

    // 创建要平滑的索引集合（围绕转弯点）
    std::set<int> indices_to_smooth;

    // 标记每个转弯点周围的点进行平滑处理
    for (int turn_idx : turning_indices)
    {
        int start_idx = std::max(1, turn_idx - half_window);
        int end_idx = std::min(static_cast<int>(path.size()) - 2, turn_idx + half_window);

        for (int idx = start_idx; idx <= end_idx; ++idx)
        {
            indices_to_smooth.insert(idx);
        }
    }

    // 如果没有要平滑的点，返回原始路径
    if (indices_to_smooth.empty())
    {
        // 创建转弯路径（起点、转弯点、终点）
        std::vector<GridNode3D> turning_path;
        turning_path.push_back(path.front()); // 起点

        for (int idx : turning_indices)
        {
            turning_path.push_back(path[idx]);
        }

        turning_path.push_back(path.back()); // 终点
        return {path, turning_path};
    }

    // 初始化结果列表
    std::vector<GridNode3D> smoothed_path;
    smoothed_path.push_back(path.front()); // 保持起点不变

    // 创建平滑的转弯路径，从起点开始
    std::vector<GridNode3D> smoothed_turning_path;
    smoothed_turning_path.push_back(path.front());

    // 创建转弯索引集合，用于快速查找
    // std::set<int> turning_indices_set(turning_indices.begin(), turning_indices.end());

    // 处理中间点
    for (size_t i = 1; i < path.size() - 1; ++i)
    {
        const GridNode3D &curr_node = path[i];
        // bool is_turning_point = (turning_indices_set.find(static_cast<int>(i)) != turning_indices_set.end());

        // 如果不在平滑集合中，保持原始点
        if (indices_to_smooth.find(static_cast<int>(i)) == indices_to_smooth.end())
        {
            smoothed_path.push_back(curr_node);

            // // 如果是转弯点，添加到转弯路径
            // if (is_turning_point)
            // {
            //     smoothed_turning_path.push_back(curr_node);
            // }
            continue;
        }

        // 检查当前点是否涉及高度变化
        const GridNode3D &prev_node = path[i - 1];
        const GridNode3D &next_node = path[i + 1];

        if (curr_node.z != prev_node.z || curr_node.z != next_node.z)
        {
            // 不平滑高度变化的点
            smoothed_path.push_back(curr_node);

            // 如果是转弯点，添加到转弯路径
            // if (is_turning_point)
            // {
            smoothed_turning_path.push_back(curr_node);
            // }
            continue;
        }

        // 确定平滑的窗口范围
        int left_available = static_cast<int>(i);
        int right_available = static_cast<int>(path.size() - 1 - i);
        int actual_half_window = std::min({half_window, left_available, right_available});

        size_t start_idx = i - actual_half_window;
        size_t end_idx = i + actual_half_window;

        // 确保窗口至少有一个点
        if (start_idx >= end_idx)
        {
            smoothed_path.push_back(curr_node);
            // if (is_turning_point)
            // {
            smoothed_turning_path.push_back(curr_node);
            // }
            continue;
        }

        // 计算加权平均的权重
        std::vector<double> weights(end_idx - start_idx + 1);
        std::vector<const GridNode3D *> window_nodes;

        // 创建节点窗口并计算权重
        for (size_t w_idx = start_idx; w_idx <= end_idx; ++w_idx)
        {
            window_nodes.push_back(&path[w_idx]);

            // 基于与中心距离计算高斯权重
            double x = 2.0 * (static_cast<double>(w_idx) - static_cast<double>(i)) /
                       static_cast<double>(actual_half_window);
            weights[w_idx - start_idx] = std::exp(-0.5 * x * x);
        }

        // 归一化权重
        double sum_weights = 0.0;
        for (double w : weights)
        {
            sum_weights += w;
        }

        for (double &w : weights)
        {
            w /= sum_weights;
        }

        // 计算加权平均
        double x_sum = 0.0, y_sum = 0.0;
        for (size_t w_idx = 0; w_idx < window_nodes.size(); ++w_idx)
        {
            x_sum += window_nodes[w_idx]->x * weights[w_idx];
            y_sum += window_nodes[w_idx]->y * weights[w_idx];
        }

        // 四舍五入到最近的整数
        int smoothed_x = static_cast<int>(std::round(x_sum));
        int smoothed_y = static_cast<int>(std::round(y_sum));
        int original_z = curr_node.z; // 保持原始Z坐标

        // 检查平滑后的位置是否有效
        Point3D smoothed_pos = {smoothed_x, smoothed_y, original_z};
        auto validity = _is_valid_position(smoothed_pos, curr_node.t, min_height, agent_id);

        if (!validity.first)
        {
            // 如果无效，使用原始点
            smoothed_path.push_back(curr_node);
            // if (is_turning_point)
            // {
            smoothed_turning_path.push_back(curr_node);
            // }
            continue;
        }

        // 创建新的平滑节点
        GridNode3D smoothed_node(x_sum, y_sum, original_z, curr_node.t);
        smoothed_node.g = curr_node.g;
        smoothed_node.h = curr_node.h;
        smoothed_node.f = curr_node.f;
        smoothed_node.jump_step = curr_node.jump_step;
        smoothed_node.need_sight = curr_node.need_sight;

        // 添加到平滑路径
        smoothed_path.push_back(smoothed_node);

        // 如果是转弯点，添加到转弯路径
        // if (is_turning_point)
        // {
        smoothed_turning_path.push_back(smoothed_node);
        // }
    }

    // 添加终点
    smoothed_path.push_back(path.back());
    smoothed_turning_path.push_back(path.back());

    return {smoothed_path, smoothed_turning_path};
}

// _find_cruise_path 实现 - 巡航阶段路径规划
// 在固定高度层执行路径搜索，这是整个路径规划中最复杂的阶段
// 使用A*算法的框架，但结合了跳点搜索(JPS)来优化搜索效率
std::tuple<
    std::optional<std::vector<GridNode3D>>, // 路径节点序列
    std::optional<std::string>              // 错误信息
    >
JPS::_find_cruise_path(
    const GridNode3D &start_node, // 起始节点（起飞结束位置）
    const Point3D &goal_pos,      // 目标位置（降落起始位置）
    int min_height,               // 最小飞行高度
    const std::string &agent_id,  // 飞行器ID
    long long cruise_start_time)  // 巡航开始时间
{
    // 优化1: 缓存目标坐标，避免重复的std::get调用
    const int goal_lon_idx = std::get<0>(goal_pos);
    const int goal_lat_idx = std::get<1>(goal_pos);
    const int goal_alt_idx = std::get<2>(goal_pos);

    // 优化2: 缓存起始节点坐标
    const int start_lon_idx = start_node.x;
    const int start_lat_idx = start_node.y;
    const int start_alt_idx = start_node.z;

    // 检查目标是否可达 - 优化3: 直接计算距离，避免函数调用
    const double dx = static_cast<double>(goal_lon_idx - start_lon_idx);
    const double dy = static_cast<double>(goal_lat_idx - start_lat_idx);
    const double dz = static_cast<double>(goal_alt_idx - start_alt_idx);
    const double distance = std::sqrt(dx * dx + dy * dy + dz * dz);
    const long long goal_time = cruise_start_time + static_cast<long long>(std::round(distance * cruise_speed_t_));

    auto goal_validity = _is_valid_position(goal_pos, goal_time, min_height, agent_id);
    if (!goal_validity.first)
    {
        return {std::nullopt, "巡航目标被阻挡: " + goal_validity.second.value_or("未知原因")};
    }

    // A*搜索的优先队列
    // 每个条目格式为 {f值, 唯一计数器, 节点}
    // 使用自定义比较器优化性能，避免tuple包装开销
    struct NodeComparator
    {
        bool operator()(const GridNode3D *a, const GridNode3D *b) const
        {
            if (std::abs(a->f - b->f) > 1e-9)
                return a->f > b->f; // 最小堆，f值小的优先
            return a->g < b->g;     // f相同时优先选择g较大的（更接近目标）
        }
    };
    std::priority_queue<GridNode3D *, std::vector<GridNode3D *>, NodeComparator> open_list;

    // 优化4: 预分配内存，减少动态分配
    std::vector<std::unique_ptr<GridNode3D>> allocated_nodes;
    allocated_nodes.reserve(1000); // 预分配合理大小

    // 已访问节点集合
    std::unordered_set<Point3D, Point3DHash> closed;
    closed.reserve(500); // 优化5: 预分配哈希表大小

    // 用于在优先队列中打破平局的计数器
    int counter = 0;

    // 初始化起始节点
    GridNode3D start_node_copy = start_node; // 复制以避免修改原始节点
    start_node_copy.g = 0.0;
    start_node_copy.h = _heuristic(_node_to_point3d(start_node_copy), goal_pos);
    start_node_copy.f = start_node_copy.h;

    // 根据到目标的距离设置跳跃步长
    if (start_node_copy.h < default_jump_step_size_ * default_jump_step_size_)
    {
        start_node_copy.jump_step = 1;
    }
    else
    {
        start_node_copy.jump_step = default_jump_step_size_; // 默认跳跃步长
    }

    // 将起始节点添加到开放列表
    // 将起始节点添加到动态内存管理并推入优先队列
    allocated_nodes.push_back(std::make_unique<GridNode3D>(start_node_copy));
    open_list.push(allocated_nodes.back().get());

    // A*搜索主循环
    while (!open_list.empty())
    {
        // 获取f值最小的节点
        GridNode3D *current_ptr = open_list.top();
        const GridNode3D &current = *current_ptr; // 使用引用避免拷贝
        open_list.pop();

        // 优化6: 缓存当前节点坐标，避免重复访问
        const int curr_lon_idx = current.x;
        const int curr_lat_idx = current.y;
        const int curr_alt_idx = current.z;
        const Point3D current_pos = {curr_lon_idx, curr_lat_idx, curr_alt_idx};

        // 如果已经探索过，则跳过
        if (closed.find(current_pos) != closed.end())
        {
            continue;
        }

        // 检查是否有到目标的直接视线
        if (current.need_sight)
        {
            auto [has_los, los_points] = has_line_of_sight(
                current, goal_pos, current.t, min_height, agent_id);

            if (has_los && !los_points.empty())
            {
                // 使用los_points作为主路径
                std::vector<GridNode3D> path = std::move(los_points); // 优化7: 使用移动语义

                // 从current的parent节点开始回溯到起点
                if (current.parent) // Check if the shared_ptr is not null
                {
                    std::vector<GridNode3D> start_to_current;
                    start_to_current.reserve(50);                          // 优化8: 预分配回溯路径空间
                    std::shared_ptr<GridNode3D> node_ptr = current.parent; // Use shared_ptr
                    while (node_ptr)                                       // Loop while shared_ptr is not null
                    {
                        start_to_current.push_back(*node_ptr); // Dereference shared_ptr to get GridNode3D object
                        node_ptr = node_ptr->parent;           // Move to the next parent
                    }

                    // 由于start_to_current是从终点到起点的顺序，需要反转后再插入
                    if (!start_to_current.empty())
                    {
                        path.insert(path.begin(), start_to_current.rbegin(), start_to_current.rend());
                    }
                }

                return {std::move(path), std::nullopt}; // 优化9: 使用移动语义返回
            }
        }

        // 将当前节点添加到已访问集合
        closed.insert(current_pos);

        // 获取邻居节点
        auto neighbors = _get_neighbors(current, min_height, agent_id);

        // 优化10: 预分配next_list容量，避免动态扩容
        std::vector<std::pair<double, GridNode3D>> next_list;
        next_list.reserve(neighbors.size());

        for (const auto &neighbor_path : neighbors)
        {
            // 如果路径为空或最后一点已经在关闭集中，则跳过
            if (neighbor_path.empty())
                continue;

            const Point3D &last_point = neighbor_path.back(); // 优化11: 使用引用避免拷贝
            if (closed.find(last_point) != closed.end())
            {
                continue;
            }

            // 处理路径中的每个点
            // 'current' is the node from which this neighbor_path originates.
            // The first node in neighbor_path will have 'current' as its conceptual parent.
            // Subsequent nodes in neighbor_path will have the previous node in neighbor_path as parent.

            std::shared_ptr<GridNode3D> parent_for_path_segment = std::make_shared<GridNode3D>(current);
            GridNode3D final_node_in_segment(0, 0, 0); // Will hold the last node of this neighbor_path

            // 优化12: 缓存neighbor_path.size()避免重复计算
            const size_t path_size = neighbor_path.size();
            for (size_t i = 0; i < path_size; ++i)
            {
                const auto &point = neighbor_path[i];
                const long long time_for_this_point = parent_for_path_segment->t + static_cast<long long>(std::round(cruise_speed_t_)); // Time based on 1 step from parent

                // 优化13: 直接使用坐标，避免std::get调用
                const int point_x = std::get<0>(point);
                const int point_y = std::get<1>(point);
                const int point_z = std::get<2>(point);

                GridNode3D current_segment_node(point_x, point_y, point_z, time_for_this_point);

                current_segment_node.parent = parent_for_path_segment;
                current_segment_node.g = parent_for_path_segment->g + 1.0; // Cost for 1 step

                // 优化14: 直接计算启发式值，避免函数调用
                const double h_dx = static_cast<double>(goal_lon_idx - point_x);
                const double h_dy = static_cast<double>(goal_lat_idx - point_y);
                const double h_dz = static_cast<double>(goal_alt_idx - point_z);
                current_segment_node.h = heuristic_weight_ * std::sqrt(std::max(std::abs(h_dx), std::abs(h_dy)) * std::max(std::abs(h_dx), std::abs(h_dy)) + h_dz * h_dz);

                current_segment_node.f = current_segment_node.g + current_segment_node.h;
                current_segment_node.jump_step = current.jump_step; // Inherit jump_step from 'current'

                // For the next iteration within this path segment, the current_segment_node becomes the parent
                parent_for_path_segment = std::make_shared<GridNode3D>(current_segment_node);

                if (i == path_size - 1) // 优化15: 使用缓存的path_size
                {
                    final_node_in_segment = current_segment_node;
                }
            }

            // Add the *last* node of this processed neighbor_path to next_list
            // This last_node (final_node_in_segment) now has its g, h, f, and parent chain correctly set up to 'current'
            next_list.emplace_back(final_node_in_segment.h, std::move(final_node_in_segment)); // 优化16: 使用emplace_back和移动语义
        }

        // 优化17: 使用更高效的选择算法，只选择最小的3个元素
        const size_t max_neighbors = std::min<size_t>(3, next_list.size());
        if (max_neighbors > 0)
        {
            if (next_list.size() <= 3)
            {
                // 如果元素少于等于3个，直接排序
                std::sort(next_list.begin(), next_list.end(),
                          [](const auto &a, const auto &b)
                          { return a.first < b.first; });
            }
            else
            {
                // 使用nth_element只部分排序，更高效
                std::nth_element(next_list.begin(), next_list.begin() + 2, next_list.end(),
                                 [](const auto &a, const auto &b)
                                 { return a.first < b.first; });
                std::sort(next_list.begin(), next_list.begin() + 3,
                          [](const auto &a, const auto &b)
                          { return a.first < b.first; });
            }
        }

        // 处理最优邻居（限制为3个，与Python实现一致）
        for (size_t i = 0; i < max_neighbors; ++i) // 优化18: 使用基于索引的循环，避免结构化绑定开销
        {
            const auto &[h_val, next_node] = next_list[i];

            // 从该邻居尝试跳点
            auto jump_node_opt = _jump(
                next_node,
                current,
                goal_pos,
                min_height,
                agent_id,
                next_node.jump_step);

            if (!jump_node_opt)
                continue;

            GridNode3D jump_node = std::move(jump_node_opt.value()); // 优化19: 使用移动语义

            // 优化20: 直接计算启发式值，避免函数调用和坐标转换
            const double jh_dx = static_cast<double>(goal_lon_idx - jump_node.x);
            const double jh_dy = static_cast<double>(goal_lat_idx - jump_node.y);
            const double jh_dz = static_cast<double>(goal_alt_idx - jump_node.z);
            jump_node.h = heuristic_weight_ * std::sqrt(std::max(std::abs(jh_dx), std::abs(jh_dy)) * std::max(std::abs(jh_dx), std::abs(jh_dy)) + jh_dz * jh_dz);
            jump_node.f = jump_node.g + jump_node.h;

            // 对接近目标的情况调整跳跃步长
            const double jump_step_threshold = static_cast<double>(default_jump_step_size_ * default_jump_step_size_); // 优化21: 缓存阈值计算
            if (jump_node.h < jump_step_threshold)
            {
                jump_node.jump_step = 1;
            }

            // 添加到开放列表
            allocated_nodes.push_back(std::make_unique<GridNode3D>(std::move(jump_node))); // 优化22: 使用移动语义
            open_list.push(allocated_nodes.back().get());
        }
    }

    // 如果到达这里，说明没有找到路径
    return {std::nullopt, "No cruise path found"};
}
