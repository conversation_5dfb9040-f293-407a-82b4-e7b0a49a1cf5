# 坐标系统重构总结

## 重构目标

统一整个项目中的坐标系统命名约定，使其与经纬度和海拔高度的对应关系更加直观和一致。

## 坐标系统约定

建立了以下统一的坐标对应关系：

- **经度(longitude) → X轴 → 东西方向 → 第一个参数/索引**
- **纬度(latitude) → Y轴 → 南北方向 → 第二个参数/索引**  
- **高度(altitude) → Z轴 → 垂直方向 → 第三个参数/索引**

## 重构内容

### 1. Map3D类 (`cpp_implementation/include/Map3D.h` & `cpp_implementation/src/Map3D.cpp`)

#### 变量重命名：
- `height_` → `lat_size_` (纬度方向栅格数)
- `width_` → `lon_size_` (经度方向栅格数)
- `depth_` → `alt_size_` (高度方向栅格数)

#### 函数参数重命名：
- `x, y, z` → `lon_idx, lat_idx, alt_idx`
- `center_x_grid, center_y_grid` → `center_lon_grid, center_lat_grid`
- `point_yx` → `point_lat_lon`

#### 新增方法（推荐使用）：
- `get_lat_size()` - 获取纬度方向栅格数
- `get_lon_size()` - 获取经度方向栅格数  
- `get_alt_size()` - 获取高度方向栅格数

#### 保留旧方法（向后兼容）：
- `get_height()` - 已弃用，请使用 `get_lat_size()`
- `get_width()` - 已弃用，请使用 `get_lon_size()`
- `get_depth()` - 已弃用，请使用 `get_alt_size()`

### 2. OccupancyMap类 (`cpp_implementation/include/OccupancyMap.h` & `cpp_implementation/src/OccupancyMap.cpp`)

#### 变量重命名：
- `height_` → `lat_size_`
- `width_` → `lon_size_`
- `depth_` → `alt_size_`

#### 构造函数参数更新：
- `OccupancyMap(height, width, depth, ...)` → `OccupancyMap(lat_size, lon_size, alt_size, ...)`

### 3. JPS类 (`cpp_implementation/include/JPS.h` & `cpp_implementation/src/JPS.cpp`)

#### 变量重命名：
- `width_` → `lon_size_`
- `height_` → `lat_size_`
- `depth_` → `alt_size_`

#### 错误信息更新：
- 所有错误输出中的坐标显示格式统一为 `(lon=x, lat=y, alt=z)`

### 4. GridNode3D类 (`cpp_implementation/include/GridNode3D.h`)

#### 注释更新：
- `float x` - 经度方向坐标 (对应X轴/东西方向)
- `float y` - 纬度方向坐标 (对应Y轴/南北方向)
- `float z` - 高度方向坐标 (对应Z轴/垂直方向)

### 5. GridConverter类 (`cpp_implementation/include/GridConverter.h`)

#### 函数参数重命名：
- `grid_to_geographic(grid_x, grid_y, grid_z)` → `grid_to_geographic(grid_lon, grid_lat, grid_alt)`

#### 注释增强：
- 所有X/Y轴相关函数都明确标注了对应的地理方向
- 增加了坐标轴与地理方向的对应关系说明

### 6. Python绑定 (`python_bindings/bindings.cpp`)

#### 构造函数参数更新：
- `Map3D(height, width, depth, ...)` → `Map3D(lat_size, lon_size, alt_size, ...)`
- `OccupancyMap(height, width, depth, ...)` → `OccupancyMap(lat_size, lon_size, alt_size, ...)`

#### 函数参数重命名：
- `is_traversable(x, y, z)` → `is_traversable(lon_idx, lat_idx, alt_idx)`
- `add_solid_cylindrical_no_fly_zone_grid(center_x_grid, center_y_grid, ...)` → `add_solid_cylindrical_no_fly_zone_grid(center_lon_grid, center_lat_grid, ...)`
- `is_point_inside_any_nfz_2d(point_yx)` → `is_point_inside_any_nfz_2d(point_lat_lon)`

#### 新增方法：
- `get_lat_size()`, `get_lon_size()`, `get_alt_size()` - 推荐使用的新方法
- 保留旧方法以确保向后兼容性

### 7. 测试文件 (`cpp_implementation/test/main.cpp`)

#### 变量重命名：
- 所有测试中的 `height, width, depth` → `lat_size, lon_size, alt_size`
- 坐标变量 `center_grid_x, center_grid_y` → `center_grid_lon, center_grid_lat`
- 边界点坐标 `edge_x, edge_y` → `edge_lon, edge_lat`

#### 输出格式统一：
- 所有坐标输出都使用 `(lon=x, lat=y)` 格式

## 向后兼容性

为了确保现有代码的兼容性，我们保留了所有旧的方法名：
- `get_height()`, `get_width()`, `get_depth()` 仍然可用，但标记为已弃用
- 旧的构造函数参数顺序仍然有效

## 建议

1. **新代码**：请使用新的命名约定和方法
2. **现有代码**：建议逐步迁移到新的API
3. **文档**：更新相关文档以反映新的坐标系统约定

## 验证

重构后的代码应该：
1. 编译无错误
2. 所有测试通过
3. 坐标系统在整个项目中保持一致
4. 与地理坐标系统的对应关系清晰明确
